import 'dart:io';

import 'package:test/test.dart';

/// Test for the route generation functionality
/// 
/// This test creates a temporary directory structure with sample pages
/// and verifies that the route generation works correctly.
void main() {
  group('Route Generation Tests', () {
    late Directory tempDir;
    late Directory pagesDir;
    
    setUp(() async {
      // Create temporary directory for testing
      tempDir = await Directory.systemTemp.createTemp('route_gen_test_');
      pagesDir = Directory('${tempDir.path}/lib/pages');
      await pagesDir.create(recursive: true);
      
      print('Test directory: ${tempDir.path}');
    });
    
    tearDown(() async {
      // Clean up temporary directory
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
      }
    });
    
    test('should generate routes from basic page files', () async {
      // Create test page files
      await _createTestPages(pagesDir);
      
      // Generate routes
      await _generateRoutes(pagesDir.path, '${tempDir.path}/lib/routes.dart');
      
      // Verify routes file was created
      final routesFile = File('${tempDir.path}/lib/routes.dart');
      expect(await routesFile.exists(), isTrue);
      
      // Verify routes content
      final content = await routesFile.readAsString();
      expect(content, contains('// GENERATED CODE - DO NOT MODIFY BY HAND'));
      expect(content, contains('final List<RouteBase> appRoutes = ['));
      expect(content, contains('final GoRouter appRouter = GoRouter('));
      
      // Check specific routes
      expect(content, contains("path: '/'"));
      expect(content, contains("path: '/home'"));
      expect(content, contains("path: '/profile'"));
      expect(content, contains("path: '/settings'"));
      expect(content, contains("path: '/settings/account'"));
      expect(content, contains("path: '/user/:id'"));
      
      print('✅ Basic route generation test passed');
    });
    
    test('should handle dynamic routes correctly', () async {
      // Create dynamic route files
      await _createDynamicRoutes(pagesDir);
      
      // Generate routes
      await _generateRoutes(pagesDir.path, '${tempDir.path}/lib/routes.dart');
      
      // Verify routes file
      final routesFile = File('${tempDir.path}/lib/routes.dart');
      final content = await routesFile.readAsString();
      
      // Check dynamic routes
      expect(content, contains("path: '/user/:id'"));
      expect(content, contains("path: '/shop/:category'"));
      expect(content, contains("path: '/blog/:slug'"));
      expect(content, contains("final id = state.pathParameters['id'] ?? '';"));
      expect(content, contains("final category = state.pathParameters['category'] ?? '';"));
      expect(content, contains("final slug = state.pathParameters['slug'] ?? '';"));
      
      print('✅ Dynamic routes test passed');
    });
    
    test('should handle nested directories', () async {
      // Create nested directory structure
      await _createNestedStructure(pagesDir);
      
      // Generate routes
      await _generateRoutes(pagesDir.path, '${tempDir.path}/lib/routes.dart');
      
      // Verify routes file
      final routesFile = File('${tempDir.path}/lib/routes.dart');
      final content = await routesFile.readAsString();
      
      // Check nested routes
      expect(content, contains("path: '/dashboard'"));
      expect(content, contains("path: '/dashboard/analytics'"));
      expect(content, contains("path: '/dashboard/settings'"));
      
      print('✅ Nested directories test passed');
    });
    
    test('should extract class names correctly', () async {
      // Create files with different class patterns
      await _createClassNameTestFiles(pagesDir);
      
      // Generate routes
      await _generateRoutes(pagesDir.path, '${tempDir.path}/lib/routes.dart');
      
      // Verify routes file
      final routesFile = File('${tempDir.path}/lib/routes.dart');
      final content = await routesFile.readAsString();
      
      // Check class names
      expect(content, contains('HomePage()'));
      expect(content, contains('ProfileWidget()'));
      expect(content, contains('SettingsPage()'));
      
      print('✅ Class name extraction test passed');
    });
    
    test('should handle empty directory gracefully', () async {
      // Generate routes from empty directory
      await _generateRoutes(pagesDir.path, '${tempDir.path}/lib/routes.dart');
      
      // Verify routes file was created but empty
      final routesFile = File('${tempDir.path}/lib/routes.dart');
      expect(await routesFile.exists(), isTrue);
      
      final content = await routesFile.readAsString();
      expect(content, contains('final List<RouteBase> appRoutes = [\n]'));
      
      print('✅ Empty directory test passed');
    });
    
    test('should handle non-existent directory', () async {
      // Try to generate routes from non-existent directory
      final nonExistentDir = '${tempDir.path}/non_existent';
      
      // This should not throw an error, just print a warning
      await _generateRoutes(nonExistentDir, '${tempDir.path}/lib/routes.dart');
      
      // Routes file should not be created
      final routesFile = File('${tempDir.path}/lib/routes.dart');
      expect(await routesFile.exists(), isFalse);
      
      print('✅ Non-existent directory test passed');
    });
  });
}

/// Create basic test page files
Future<void> _createTestPages(Directory pagesDir) async {
  // Create index.dart
  await File('${pagesDir.path}/index.dart').writeAsString('''
import 'package:flutter/material.dart';

class HomePage extends StatelessWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Home')),
      body: const Center(child: Text('Home Page')),
    );
  }
}
''');

  // Create home.dart
  await File('${pagesDir.path}/home.dart').writeAsString('''
import 'package:flutter/material.dart';

class HomePageAlt extends StatelessWidget {
  const HomePageAlt({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Text('Home Alt'));
  }
}
''');

  // Create profile.dart
  await File('${pagesDir.path}/profile.dart').writeAsString('''
import 'package:flutter/material.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({Key? key}) : super(key: key);

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Text('Profile'));
  }
}
''');

  // Create settings directory and files
  final settingsDir = Directory('${pagesDir.path}/settings');
  await settingsDir.create();
  
  await File('${settingsDir.path}/index.dart').writeAsString('''
import 'package:flutter/material.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Text('Settings'));
  }
}
''');

  await File('${settingsDir.path}/account.dart').writeAsString('''
import 'package:flutter/material.dart';

class AccountPage extends StatelessWidget {
  const AccountPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Text('Account'));
  }
}
''');

  // Create user directory with dynamic route
  final userDir = Directory('${pagesDir.path}/user');
  await userDir.create();
  
  await File('${userDir.path}/[id].dart').writeAsString('''
import 'package:flutter/material.dart';

class UserPage extends StatelessWidget {
  final String id;
  
  const UserPage({Key? key, required this.id}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(body: Text('User: \$id'));
  }
}
''');
}

/// Create dynamic route test files
Future<void> _createDynamicRoutes(Directory pagesDir) async {
  // Create user/[id].dart
  final userDir = Directory('${pagesDir.path}/user');
  await userDir.create();
  
  await File('${userDir.path}/[id].dart').writeAsString('''
import 'package:flutter/material.dart';

class UserDetailPage extends StatelessWidget {
  final String id;
  
  const UserDetailPage({Key? key, required this.id}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(body: Text('User: \$id'));
  }
}
''');

  // Create shop/[category].dart (single dynamic route)
  final shopDir = Directory('${pagesDir.path}/shop');
  await shopDir.create();
  
  await File('${shopDir.path}/[category].dart').writeAsString('''
import 'package:flutter/material.dart';

class CategoryPage extends StatelessWidget {
  final String category;
  
  const CategoryPage({Key? key, required this.category}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(body: Text('Category: \$category'));
  }
}
''');

  // Create blog/[slug].dart (another dynamic route)
  final blogDir = Directory('${pagesDir.path}/blog');
  await blogDir.create();
  
  await File('${blogDir.path}/[slug].dart').writeAsString('''
import 'package:flutter/material.dart';

class BlogPostPage extends StatelessWidget {
  final String slug;
  
  const BlogPostPage({Key? key, required this.slug}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(body: Text('Blog Post: \$slug'));
  }
}
''');
}

/// Create nested directory structure
Future<void> _createNestedStructure(Directory pagesDir) async {
  // Create dashboard directory
  final dashboardDir = Directory('${pagesDir.path}/dashboard');
  await dashboardDir.create();
  
  await File('${dashboardDir.path}/index.dart').writeAsString('''
import 'package:flutter/material.dart';

class DashboardPage extends StatelessWidget {
  const DashboardPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Text('Dashboard'));
  }
}
''');

  await File('${dashboardDir.path}/analytics.dart').writeAsString('''
import 'package:flutter/material.dart';

class AnalyticsPage extends StatelessWidget {
  const AnalyticsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Text('Analytics'));
  }
}
''');

  await File('${dashboardDir.path}/settings.dart').writeAsString('''
import 'package:flutter/material.dart';

class DashboardSettingsPage extends StatelessWidget {
  const DashboardSettingsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Text('Dashboard Settings'));
  }
}
''');
}

/// Create files to test class name extraction
Future<void> _createClassNameTestFiles(Directory pagesDir) async {
  // StatelessWidget
  await File('${pagesDir.path}/home.dart').writeAsString('''
import 'package:flutter/material.dart';

class HomePage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Text('Home'));
  }
}
''');

  // StatefulWidget
  await File('${pagesDir.path}/profile.dart').writeAsString('''
import 'package:flutter/material.dart';

class ProfileWidget extends StatefulWidget {
  @override
  State<ProfileWidget> createState() => _ProfileWidgetState();
}

class _ProfileWidgetState extends State<ProfileWidget> {
  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Text('Profile'));
  }
}
''');

  // File with no class (should use filename)
  await File('${pagesDir.path}/settings.dart').writeAsString('''
import 'package:flutter/material.dart';

// No class defined, should use filename
Widget buildSettings() {
  return const Scaffold(body: Text('Settings'));
}
''');
}

/// Generate routes using the same logic as the main script
Future<void> _generateRoutes(String directoryPath, String outputPath) async {
  final List<RouteInfo> routeInfos = [];
  final Directory directory = Directory(directoryPath);

  if (!directory.existsSync()) {
    print('❌ Directory $directoryPath does not exist');
    return;
  }

  // Scan directory and collect route information
  _scanDirectoryForCodeGeneration(
    directory,
    routeInfos,
    '/',
    directoryPath,
  );

  // Generate the routes.dart file content
  final String generatedCode = _generateRoutesFileContent(routeInfos);

  // Write to file
  final File outputFile = File(outputPath);
  await outputFile.parent.create(recursive: true);
  await outputFile.writeAsString(generatedCode);

  print('Generated ${routeInfos.length} routes to $outputPath');
}

/// Scans directory and collects route information for code generation
void _scanDirectoryForCodeGeneration(
  Directory directory,
  List<RouteInfo> routeInfos,
  String currentPath,
  String basePath,
) {
  final List<FileSystemEntity> entities = directory.listSync();

  // Separate files and directories
  final List<File> files = [];
  final List<Directory> directories = [];

  for (final entity in entities) {
    if (entity is File && entity.path.endsWith('.dart')) {
      files.add(entity);
    } else if (entity is Directory) {
      directories.add(entity);
    }
  }

  // Process files first
  for (final file in files) {
    final String fileName = _getFileName(file.path);
    String routePath = _buildRoutePath(currentPath, fileName);

    // Handle special cases
    if (fileName == 'index') {
      routePath = currentPath == '/' ? '/' : currentPath;
    } else if (fileName.startsWith('[') && fileName.endsWith(']')) {
      // Dynamic route parameter
      final String paramName = fileName.substring(1, fileName.length - 1);
      routePath = '$currentPath/:$paramName';
    }

    // Get import path relative to lib/
    final String importPath = _getImportPath(file.path, basePath);
    final String className = _extractClassName(file);

    routeInfos.add(RouteInfo(
      path: routePath,
      name: _generateRouteName(routePath),
      importPath: importPath,
      className: className,
      fileName: fileName,
      isDynamic: fileName.startsWith('[') && fileName.endsWith(']'),
    ));
  }

  // Process directories recursively
  for (final directory in directories) {
    final String dirName = _getDirName(directory.path);
    final String newPath = currentPath == '/'
        ? '/$dirName'
        : '$currentPath/$dirName';

    _scanDirectoryForCodeGeneration(
      directory,
      routeInfos,
      newPath,
      basePath,
    );
  }
}

/// Generates the content for routes.dart file
String _generateRoutesFileContent(List<RouteInfo> routeInfos) {
  final StringBuffer buffer = StringBuffer();

  // File header
  buffer.writeln('// GENERATED CODE - DO NOT MODIFY BY HAND');
  buffer.writeln('// Generated by generate_routes.dart');
  buffer.writeln('// Generated on: ${DateTime.now().toIso8601String()}');
  buffer.writeln();

  // Imports
  buffer.writeln("import 'package:flutter/material.dart';");
  buffer.writeln("import 'package:go_router/go_router.dart';");
  buffer.writeln();

  // Import all page files
  final Set<String> imports = routeInfos.map((r) => r.importPath).toSet();
  for (final import in imports) {
    buffer.writeln("import '$import';");
  }
  buffer.writeln();

  // Routes list
  buffer.writeln('/// Generated routes from directory structure');
  buffer.writeln('final List<RouteBase> appRoutes = [');

  for (final route in routeInfos) {
    buffer.writeln('  GoRoute(');
    buffer.writeln("    path: '${route.path}',");
    buffer.writeln("    name: '${route.name}',");
    buffer.writeln('    builder: (context, state) {');
    
    if (route.isDynamic) {
      // Handle dynamic routes with parameters
      final paramName = route.fileName.substring(1, route.fileName.length - 1);
      buffer.writeln("      final $paramName = state.pathParameters['$paramName'] ?? '';");
      buffer.writeln('      return ${route.className}($paramName: $paramName);');
    } else {
      // Static routes
      buffer.writeln('      return const ${route.className}();');
    }
    
    buffer.writeln('    },');
    buffer.writeln('  ),');
  }

  buffer.writeln('];');
  buffer.writeln();

  // GoRouter instance
  buffer.writeln('/// Generated GoRouter instance');
  buffer.writeln('final GoRouter appRouter = GoRouter(');
  buffer.writeln('  routes: appRoutes,');
  buffer.writeln('  initialLocation: \'/\',');
  buffer.writeln('  errorBuilder: (context, state) => Scaffold(');
  buffer.writeln('    appBar: AppBar(title: const Text(\'Page Not Found\')),');
  buffer.writeln('    body: Center(');
  buffer.writeln('      child: Column(');
  buffer.writeln('        mainAxisAlignment: MainAxisAlignment.center,');
  buffer.writeln('        children: [');
  buffer.writeln('          const Icon(Icons.error_outline, size: 64),');
  buffer.writeln('          const SizedBox(height: 16),');
  buffer.writeln('          Text(\'Route not found: \${state.location}\'),');
  buffer.writeln('          const SizedBox(height: 16),');
  buffer.writeln('          ElevatedButton(');
  buffer.writeln('            onPressed: () => context.go(\'/\'),');
  buffer.writeln('            child: const Text(\'Go Home\'),');
  buffer.writeln('          ),');
  buffer.writeln('        ],');
  buffer.writeln('      ),');
  buffer.writeln('    ),');
  buffer.writeln('  ),');
  buffer.writeln(');');

  return buffer.toString();
}

/// Extracts the class name from a Dart file
String _extractClassName(File file) {
  try {
    final String content = file.readAsStringSync();
    final RegExp classRegex = RegExp(r'class\s+(\w+)\s+extends\s+StatelessWidget');
    final Match? match = classRegex.firstMatch(content);
    if (match != null) {
      return match.group(1)!;
    }
    
    // Try StatefulWidget
    final RegExp statefulRegex = RegExp(r'class\s+(\w+)\s+extends\s+StatefulWidget');
    final Match? statefulMatch = statefulRegex.firstMatch(content);
    if (statefulMatch != null) {
      return statefulMatch.group(1)!;
    }
    
    // Fallback to filename
    return '${_getFileName(file.path).split('_').map((word) => 
      word.isEmpty ? '' : word[0].toUpperCase() + word.substring(1)).join('')}Page';
  } catch (e) {
    // Fallback to filename-based class name
    return '${_getFileName(file.path).split('_').map((word) => 
      word.isEmpty ? '' : word[0].toUpperCase() + word.substring(1)).join('')}Page';
  }
}

/// Gets the import path relative to lib/
String _getImportPath(String filePath, String basePath) {
  // Convert to relative path from lib/
  String relativePath = filePath;
  
  // If the file is in the project, make it relative to lib/
  if (filePath.contains('/lib/')) {
    relativePath = filePath.split('/lib/').last;
  } else {
    // If not in lib/, try to make it relative to the base path
    relativePath = filePath.replaceFirst(basePath, '').replaceFirst(RegExp(r'^/'), '');
  }
  
  return relativePath;
}

// Helper methods
String _getFileName(String filePath) {
  final String fileName = filePath.split('/').last;
  return fileName.split('.').first;
}

String _getDirName(String dirPath) {
  return dirPath.split('/').last;
}

String _buildRoutePath(String currentPath, String fileName) {
  if (currentPath == '/') {
    return '/$fileName';
  }
  return '$currentPath/$fileName';
}

String _generateRouteName(String path) {
  return path.replaceAll('/', '_').replaceAll(':', '').substring(1);
}

/// Route information for code generation
class RouteInfo {
  final String path;
  final String name;
  final String importPath;
  final String className;
  final String fileName;
  final bool isDynamic;

  RouteInfo({
    required this.path,
    required this.name,
    required this.importPath,
    required this.className,
    required this.fileName,
    required this.isDynamic,
  });
} 