import 'dart:io';

void main() async {
  print('🔍 Debugging route generation...');
  
  // Create a simple test directory
  final tempDir = await Directory.systemTemp.createTemp('debug_routes_');
  final pagesDir = Directory('${tempDir.path}/lib/pages');
  await pagesDir.create(recursive: true);
  
  print('📁 Test directory: ${tempDir.path}');
  
  try {
    // Create shop/[category]/[id].dart structure
    final shopDir = Directory('${pagesDir.path}/shop');
    await shopDir.create();
    
    final categoryDir = Directory('${shopDir.path}/[category]');
    await categoryDir.create();
    
    await File('${categoryDir.path}/[id].dart').writeAsString('''
import 'package:flutter/material.dart';

class ProductPage extends StatelessWidget {
  final String category;
  final String id;
  
  const ProductPage({Key? key, required this.category, required this.id}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(body: Text('Product: \$category/\$id'));
  }
}
''');

    print('✅ Created test structure');
    print('   📁 ${shopDir.path}');
    print('   📁 ${categoryDir.path}');
    print('   📄 ${categoryDir.path}/[id].dart');
    
    // Generate routes
    final result = await Process.run(
      'dart',
      ['run', 'bin/generate_routes.dart', pagesDir.path, '${tempDir.path}/lib/routes.dart'],
      workingDirectory: Directory.current.path,
    );
    
    print('\n📝 Route generation output:');
    print('Exit code: ${result.exitCode}');
    print('Stdout: ${result.stdout}');
    if (result.stderr.isNotEmpty) {
      print('Stderr: ${result.stderr}');
    }
    
    // Check generated routes
    final routesFile = File('${tempDir.path}/lib/routes.dart');
    if (await routesFile.exists()) {
      final content = await routesFile.readAsString();
      print('\n📋 Generated routes content:');
      print(content);
      
      // Check for specific patterns
      if (content.contains('/shop/:category/:id')) {
        print('✅ Found nested dynamic route: /shop/:category/:id');
      } else {
        print('❌ Missing nested dynamic route: /shop/:category/:id');
        
        // Show what routes were actually generated
        final routeMatches = RegExp(r"path: '([^']+)'").allMatches(content);
        print('📋 Actually generated routes:');
        for (final match in routeMatches) {
          print('   - ${match.group(1)}');
        }
      }
    } else {
      print('❌ Routes file was not created');
    }
    
  } catch (e) {
    print('❌ Error: $e');
  } finally {
    // Clean up
    if (await tempDir.exists()) {
      await tempDir.delete(recursive: true);
    }
    print('\n🧹 Cleaned up test directory');
  }
} 