import 'dart:io';

/// Simple test script for route generation functionality
/// 
/// This creates a temporary directory structure and tests
/// the route generation to ensure it works correctly.
void main() async {
  print('🧪 Starting Route Generation Tests...\n');
  
  // Create temporary test directory
  final tempDir = await Directory.systemTemp.createTemp('route_gen_test_');
  final pagesDir = Directory('${tempDir.path}/lib/pages');
  await pagesDir.create(recursive: true);
  
  print('📁 Test directory: ${tempDir.path}');
  
  try {
    // Test 1: Basic route generation
    await testBasicRouteGeneration(pagesDir, tempDir);
    
    // Test 2: Dynamic routes
    await testDynamicRoutes(pagesDir, tempDir);
    
    // Test 3: Nested directories
    await testNestedDirectories(pagesDir, tempDir);
    
    // Test 4: Empty directory
    await testEmptyDirectory(pagesDir, tempDir);
    
    print('\n✅ All tests passed successfully!');
    
  } catch (e) {
    print('\n❌ Test failed: $e');
  } finally {
    // Clean up
    if (await tempDir.exists()) {
      await tempDir.delete(recursive: true);
    }
    print('🧹 Cleaned up test directory');
  }
}

/// Test basic route generation
Future<void> testBasicRouteGeneration(Directory pagesDir, Directory tempDir) async {
  print('🔍 Test 1: Basic route generation');
  
  // Create test pages
  await createBasicTestPages(pagesDir);
  
  // Generate routes using our command
  final result = await Process.run(
    'dart',
    ['run', 'bin/generate_routes.dart', pagesDir.path, '${tempDir.path}/lib/routes.dart'],
    workingDirectory: Directory.current.path,
  );
  
  if (result.exitCode != 0) {
    throw Exception('Route generation failed: ${result.stderr}');
  }
  
  // Verify routes file was created
  final routesFile = File('${tempDir.path}/lib/routes.dart');
  if (!await routesFile.exists()) {
    throw Exception('Routes file was not created');
  }
  
  // Verify content
  final content = await routesFile.readAsString();
  final expectedRoutes = [
    "path: '/'",
    "path: '/home'",
    "path: '/profile'",
    "path: '/settings'",
    "path: '/settings/account'",
    "path: '/user/:id'",
  ];
  
  for (final route in expectedRoutes) {
    if (!content.contains(route)) {
      throw Exception('Expected route not found: $route');
    }
  }
  
  print('   ✅ Basic routes generated correctly');
  
  // Clean up for next test
  await clearDirectory(pagesDir);
}

/// Test dynamic routes
Future<void> testDynamicRoutes(Directory pagesDir, Directory tempDir) async {
  print('🔍 Test 2: Dynamic routes');
  
  // Create dynamic route files
  await createDynamicRoutes(pagesDir);
  
  // Generate routes
  final result = await Process.run(
    'dart',
    ['run', 'bin/generate_routes.dart', pagesDir.path, '${tempDir.path}/lib/routes.dart'],
    workingDirectory: Directory.current.path,
  );
  
  if (result.exitCode != 0) {
    throw Exception('Dynamic route generation failed: ${result.stderr}');
  }
  
  // Verify content
  final routesFile = File('${tempDir.path}/lib/routes.dart');
  final content = await routesFile.readAsString();
  
  final expectedPatterns = [
    "path: '/user/:id'",
    "path: '/shop/:category'",
    "final id = state.pathParameters['id'] ?? '';",
    "final category = state.pathParameters['category'] ?? '';",
  ];
  
  for (final pattern in expectedPatterns) {
    if (!content.contains(pattern)) {
      throw Exception('Expected dynamic route pattern not found: $pattern');
    }
  }
  
  print('   ✅ Dynamic routes generated correctly');
  
  // Clean up for next test
  await clearDirectory(pagesDir);
}

/// Test nested directories
Future<void> testNestedDirectories(Directory pagesDir, Directory tempDir) async {
  print('🔍 Test 3: Nested directories');
  
  // Create nested structure
  await createNestedStructure(pagesDir);
  
  // Generate routes
  final result = await Process.run(
    'dart',
    ['run', 'bin/generate_routes.dart', pagesDir.path, '${tempDir.path}/lib/routes.dart'],
    workingDirectory: Directory.current.path,
  );
  
  if (result.exitCode != 0) {
    throw Exception('Nested route generation failed: ${result.stderr}');
  }
  
  // Verify content
  final routesFile = File('${tempDir.path}/lib/routes.dart');
  final content = await routesFile.readAsString();
  
  final expectedRoutes = [
    "path: '/dashboard'",
    "path: '/dashboard/analytics'",
    "path: '/dashboard/settings'",
  ];
  
  for (final route in expectedRoutes) {
    if (!content.contains(route)) {
      throw Exception('Expected nested route not found: $route');
    }
  }
  
  print('   ✅ Nested directories handled correctly');
  
  // Clean up for next test
  await clearDirectory(pagesDir);
}

/// Test empty directory
Future<void> testEmptyDirectory(Directory pagesDir, Directory tempDir) async {
  print('🔍 Test 4: Empty directory');
  
  // Generate routes from empty directory
  final result = await Process.run(
    'dart',
    ['run', 'bin/generate_routes.dart', pagesDir.path, '${tempDir.path}/lib/routes.dart'],
    workingDirectory: Directory.current.path,
  );
  
  if (result.exitCode != 0) {
    throw Exception('Empty directory test failed: ${result.stderr}');
  }
  
  // Verify content
  final routesFile = File('${tempDir.path}/lib/routes.dart');
  final content = await routesFile.readAsString();
  
  if (!content.contains('final List<RouteBase> appRoutes = []')) {
    throw Exception('Empty routes array not found');
  }
  
  print('   ✅ Empty directory handled correctly');
}

/// Create basic test pages
Future<void> createBasicTestPages(Directory pagesDir) async {
  // Create index.dart
  await File('${pagesDir.path}/index.dart').writeAsString('''
import 'package:flutter/material.dart';

class HomePage extends StatelessWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Text('Home'));
  }
}
''');

  // Create home.dart
  await File('${pagesDir.path}/home.dart').writeAsString('''
import 'package:flutter/material.dart';

class HomePageAlt extends StatelessWidget {
  const HomePageAlt({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Text('Home Alt'));
  }
}
''');

  // Create profile.dart
  await File('${pagesDir.path}/profile.dart').writeAsString('''
import 'package:flutter/material.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({Key? key}) : super(key: key);

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Text('Profile'));
  }
}
''');

  // Create settings directory
  final settingsDir = Directory('${pagesDir.path}/settings');
  await settingsDir.create();
  
  await File('${settingsDir.path}/index.dart').writeAsString('''
import 'package:flutter/material.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Text('Settings'));
  }
}
''');

  await File('${settingsDir.path}/account.dart').writeAsString('''
import 'package:flutter/material.dart';

class AccountPage extends StatelessWidget {
  const AccountPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Text('Account'));
  }
}
''');

  // Create user directory with dynamic route
  final userDir = Directory('${pagesDir.path}/user');
  await userDir.create();
  
  await File('${userDir.path}/[id].dart').writeAsString('''
import 'package:flutter/material.dart';

class UserPage extends StatelessWidget {
  final String id;
  
  const UserPage({Key? key, required this.id}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(body: Text('User: \$id'));
  }
}
''');
}

/// Create dynamic route test files
Future<void> createDynamicRoutes(Directory pagesDir) async {
  // Create user/[id].dart
  final userDir = Directory('${pagesDir.path}/user');
  await userDir.create();
  
  await File('${userDir.path}/[id].dart').writeAsString('''
import 'package:flutter/material.dart';

class UserDetailPage extends StatelessWidget {
  final String id;
  
  const UserDetailPage({Key? key, required this.id}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(body: Text('User: \$id'));
  }
}
''');

  // Create shop/[category].dart
  final shopDir = Directory('${pagesDir.path}/shop');
  await shopDir.create();
  
  await File('${shopDir.path}/[category].dart').writeAsString('''
import 'package:flutter/material.dart';

class CategoryPage extends StatelessWidget {
  final String category;
  
  const CategoryPage({Key? key, required this.category}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(body: Text('Category: \$category'));
  }
}
''');
}

/// Create nested directory structure
Future<void> createNestedStructure(Directory pagesDir) async {
  final dashboardDir = Directory('${pagesDir.path}/dashboard');
  await dashboardDir.create();
  
  await File('${dashboardDir.path}/index.dart').writeAsString('''
import 'package:flutter/material.dart';

class DashboardPage extends StatelessWidget {
  const DashboardPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Text('Dashboard'));
  }
}
''');

  await File('${dashboardDir.path}/analytics.dart').writeAsString('''
import 'package:flutter/material.dart';

class AnalyticsPage extends StatelessWidget {
  const AnalyticsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Text('Analytics'));
  }
}
''');

  await File('${dashboardDir.path}/settings.dart').writeAsString('''
import 'package:flutter/material.dart';

class DashboardSettingsPage extends StatelessWidget {
  const DashboardSettingsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Text('Dashboard Settings'));
  }
}
''');
}

/// Clear directory contents
Future<void> clearDirectory(Directory dir) async {
  if (await dir.exists()) {
    await for (final entity in dir.list()) {
      await entity.delete(recursive: true);
    }
  }
} 