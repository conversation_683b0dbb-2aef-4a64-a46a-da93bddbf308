# Common Widgets

A comprehensive Flutter package providing a collection of reusable widgets, utilities, and extensions for building modern Flutter applications.

## Features

### 🎨 **UI Components**
- **Custom App Bar** - Configurable app bar with back button and actions
- **Custom Buttons** - Various button styles (normal, floating, icon, inkwell)
- **Custom Text** - Enhanced text widget with styling options
- **Custom TextField** - Advanced text input with validation
- **Custom Dropdown** - Dropdown picker with search functionality
- **Custom Date/Time Pickers** - Date, month, and year pickers
- **Custom Checkbox & Switch** - Styled form controls
- **Custom Badge** - Notification badges and indicators
- **Custom Tab Bar** - Configurable tab navigation
- **Custom Map View** - Interactive map component with markers

### 🖼️ **Media & Camera**
- **Photo Taker** - Camera and gallery integration
- **Image Loading Builder** - Custom image loading with placeholders
- **Gallery Screen** - Photo gallery with selection

### 📱 **Navigation & Layout**
- **Responsive Wrapper** - Responsive design utilities
- **Panel** - Flexible layout container
- **Bottom Sheet Content Wrapper** - Modal bottom sheets
- **Back to Top** - Scroll-to-top functionality
- **Protected Screen** - Authentication wrapper

### 🔧 **Utilities**
- **Loader** - Loading indicators and spinners
- **Alert** - Custom alert dialogs
- **Error Boundary** - Error handling wrapper
- **Global Loader Wrapper** - App-wide loading state
- **Dismiss Keyboard Wrapper** - Keyboard dismissal utilities
- **Copy to Clipboard** - Text copying functionality
- **Search Bar** - Custom search input
- **No Result** - Empty state displays

### 🌐 **Network & Data**
- **Dio Client** - HTTP client with error handling
- **Result Wrapper** - API response handling
- **Network Exceptions** - Comprehensive error types
- **Shared Preferences Manager** - Local storage utilities

### 🎯 **Extensions**
- **Context Extensions** - Screen size and navigation helpers
- **Text Extensions** - Text styling and formatting
- **DateTime Extensions** - Date manipulation utilities
- **String Extensions** - String formatting and validation
- **Screen Extensions** - Responsive design helpers
- **Debounce Extension** - Input debouncing utilities

### 🎨 **Theme & Styling**
- **Custom Colors** - Extensible color system
- **App Theme** - Theme configuration
- **Border Utilities** - Custom border styles

## Installation

Add this to your package's `pubspec.yaml` file:

```yaml
dependencies:
  common_widgets:
    git:
      url: https://github.com/your-username/common_widgets.git
      ref: main
```

Or if published to pub.dev:

```yaml
dependencies:
  common_widgets: ^0.0.1
```

## Quick Start

### 1. Initialize the Package

```dart
import 'package:common_widgets/common_widgets.dart';

void main() {
  // Initialize with your primary color
  initCommonWidgets(
    Colors.blue, // Your app's primary color
    GlobalKey<NavigatorState>(),
  );
  
  runApp(MyApp());
}
```

### 2. Basic Usage

```dart
import 'package:common_widgets/common_widgets.dart';

class MyHomePage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'My App',
        actions: [
          IconButton(
            icon: Icon(Icons.settings),
            onPressed: () {},
          ),
        ],
      ),
      body: Column(
        children: [
          CustomText(
            text: 'Welcome to Common Widgets!',
            size: 24,
            fontWeight: FontWeight.bold,
            color: MyColors.primary,
          ),
          SizedBox(height: 20),
          CustomNormalButton(
            text: 'Click Me',
            onPressed: () {
              Alert.show(
                context,
                message: 'Button clicked!',
              );
            },
          ),
        ],
      ),
    );
  }
}
```

## Widget Examples

### Custom App Bar

```dart
CustomAppBar(
  title: 'My Screen',
  backgroundColor: MyColors.primary,
  actions: [
    IconButton(
      icon: Icon(Icons.search),
      onPressed: () {},
    ),
  ],
  onBack: () => Navigator.pop(context),
)
```

### Custom Text Field

```dart
CustomTextField(
  hintText: 'Enter your name',
  prefixIcon: Icon(Icons.person),
  validator: (value) {
    if (value?.isEmpty ?? true) {
      return 'Name is required';
    }
    return null;
  },
)
```

### Custom Dropdown

```dart
CustomDropdownPicker(
  title: 'Select Country',
  initItem: OptionItem(key: 'Select', value: ''),
  items: [
    OptionItem(key: 'USA', value: 'us'),
    OptionItem(key: 'Canada', value: 'ca'),
    OptionItem(key: 'UK', value: 'uk'),
  ],
  onSelectedItem: (item) {
    print('Selected: ${item.key}');
  },
  parentContext: context,
)
```

### Custom Date Picker

```dart
CustomDatePicker(
  initialDate: DateTime.now(),
  firstDate: DateTime(2020),
  lastDate: DateTime(2030),
  onDateSelected: (date) {
    print('Selected date: $date');
  },
)
```

### Loader

```dart
// Show loading
Loader.show(context);

// Hide loading
Loader.hide(context);

// Or use the wrapper
GlobalLoaderWrapper(
  isLoading: true,
  child: YourContent(),
)
```

### Alert Dialogs

```dart
// Simple alert
Alert.show(
  context,
  message: 'This is an alert message',
);

// Confirmation dialog
Alert.confirm(
  context,
  title: 'Confirm Action',
  message: 'Are you sure you want to proceed?',
  onConfirm: () {
    // Handle confirmation
  },
);

// Custom alert with configuration
Alert.show(
  context,
  message: 'Custom alert',
  config: AlertDialogConfig(
    backgroundColor: MyColors.aquaHaze,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(16),
    ),
  ),
)
```

### Photo Taker

```dart
PhotoTaker(
  onImageSelected: (file) {
    print('Selected image: ${file.path}');
  },
  maxImages: 5,
  allowCamera: true,
  allowGallery: true,
)
```

### Custom Map View

```dart
CustomMapView(
  initialPosition: LatLng(37.7749, -122.4194),
  markers: [
    Marker(
      point: LatLng(37.7749, -122.4194),
      builder: (context) => Icon(Icons.location_on),
    ),
  ],
  onMapTap: (position) {
    print('Map tapped at: $position');
  },
)
```

## Network Usage

### HTTP Client

```dart
final dioClient = DioClient(Dio());

// GET request
final response = await dioClient.getData<User>(
  '/api/users/1',
  parser: (json) => User.fromJson(json),
);

// POST request
final newUser = await dioClient.postData<User>(
  '/api/users',
  data: userData,
  parser: (json) => User.fromJson(json),
);
```

### Error Handling

```dart
try {
  final data = await dioClient.getData('/api/data');
} on BadRequestException catch (e) {
  print('Bad request: ${e.message}');
} on UnauthorisedException catch (e) {
  print('Unauthorized: ${e.message}');
} on NetworkException catch (e) {
  print('Network error: ${e.message}');
}
```

## Extensions Usage

### Context Extensions

```dart
// Screen dimensions
final screenWidth = context.screenWidth;
final screenHeight = context.screenHeight;

// Responsive sizing
final padding = context.d16; // 16 * screen density
final fontSize = context.d20; // 20 * screen density

// Navigation
context.push('/home');
context.pop();
```

### Text Extensions

```dart
// String formatting
final slug = 'Hello World'.toSlug(); // hello-world
final capitalized = 'hello'.capitalize(); // Hello

// Text styling
Text('Hello').bodyMedium();
Text('Title').headlineLarge();
```

### DateTime Extensions

```dart
final now = DateTime.now();
final formatted = now.format('yyyy-MM-dd');
final isToday = now.isToday();
final isYesterday = now.isYesterday();
```

## Theme Configuration

### Custom Colors

```dart
// Access colors
Container(
  color: MyColors.primary,
  child: Text('Primary colored text'),
)

// Set primary color (only once)
initCommonWidgets(Colors.red, navigatorKey);
```

### App Theme

```dart
MaterialApp(
  theme: AppTheme.lightTheme,
  darkTheme: AppTheme.darkTheme,
  // ...
)
```

## Dependencies

This package includes the following dependencies:

- `flutter_riverpod` - State management
- `dio` - HTTP client
- `shared_preferences` - Local storage
- `go_router` - Navigation
- `permission_handler` - Permissions
- `flutter_image_compress` - Image compression
- `geolocator` - Location services
- `flutter_map` - Maps
- `camera` - Camera access
- `mobile_scanner` - QR/Barcode scanning

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

If you encounter any issues or have questions, please:

1. Check the [Issues](https://github.com/your-username/common_widgets/issues) page
2. Create a new issue with detailed information
3. Include code examples and error messages

## Changelog

See [CHANGELOG.md](CHANGELOG.md) for a list of changes and version history.
