name: common_widgets
description: "Common widgets for Flutter"
version: 0.0.3
homepage:

environment:
  sdk: ^3.8.0
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  camera: ^0.11.1
  dio: ^5.4.0
  flutter_image_compress: ^2.3.0
  flutter_map: ^8.1.1
  flutter_map_cancellable_tile_provider: ^3.1.0
  flutter_riverpod: ^2.4.9
  geolocator: ^13.0.1
  go_router: ^14.1.4
  image: ^4.5.4
  intl: ^0.20.2
  latlong2: ^0.9.1
  loading_animation_widget: ^1.3.0
  mobile_scanner: ^7.0.1
  permission_handler: ^11.3.1
  photo_view: ^0.15.0
  riverpod_annotation: ^2.3.3
  shared_preferences: ^2.2.2
  args: ^2.7.0
  change_app_package_name: ^1.4.0
  rename: ^3.1.0
  month_picker_dialog: ^6.2.3


dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  test: ^1.24.0

executables:
  # The key is the command name, the value is the script file (without .dart)
  change_name: cli
  generate_routes: generate_routes

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # To add assets to your package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/to/asset-from-package
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/to/font-from-package
