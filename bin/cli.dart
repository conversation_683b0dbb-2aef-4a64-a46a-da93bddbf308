// ignore_for_file: avoid_print

import 'dart:io';

import 'package:args/args.dart';

// A simple utility for adding color to terminal output.
class AnsiColors {
  static const String reset = '\x1B[0m';
  static const String red = '\x1B[31m';
  static const String green = '\x1B[32m';
  static const String yellow = '\x1B[33m';
  static const String cyan = '\x1B[36m';
  static const String magenta = '\x1B[35m';
}

void main(List<String> arguments) async {
  final parser = ArgParser();

  // Our tool's commands remain the same for user convenience.
  parser.addCommand(
    'app-name',
    ArgParser()..addOption(
      'name',
      help: 'The new application display name (e.g., "My Awesome App").',
    ),
  );
  parser.addCommand(
    'package-name',
    ArgParser()..addOption(
      'name',
      help: 'The new package name (e.g., com.company.app).',
    ),
  );

  ArgResults argResults;
  try {
    argResults = parser.parse(arguments);
  } catch (e) {
    printError('Invalid command: ${e.toString()}');
    exit(1);
  }

  if (argResults.command != null) {
    await handleCommand(argResults.command!);
  } else {
    await showInteractiveMenu();
  }
}

/// Shows a menu to the user and lets them choose an action.
Future<void> showInteractiveMenu() async {
  printTitle('Project Configuration Tool');
  print('Please choose an option:');
  print(
    '  ${AnsiColors.cyan}1)${AnsiColors.reset} Change App Name (the name shown on the home screen)',
  );
  print(
    '  ${AnsiColors.cyan}2)${AnsiColors.reset} Change Package Name (the unique ID on app stores)',
  );
  print('  ${AnsiColors.cyan}0)${AnsiColors.reset} Exit');
  stdout.write('Enter your choice: ');

  final choice = stdin.readLineSync();
  switch (choice) {
    case '1':
      await changeAppName();
      break;
    case '2':
      await changePackageName();
      break;
    case '0':
      print('Exiting.');
      exit(0);
    default:
      printError('Invalid choice. Please try again.');
      exit(1);
  }
}

/// Handles the logic when a command is passed via arguments.
Future<void> handleCommand(ArgResults commandResults) async {
  final commandName = commandResults.name;
  final newName = commandResults['name'] as String?;

  if (newName == null || newName.isEmpty) {
    printError(
      'The --name argument is required for the "$commandName" command.',
    );
    exit(1);
  }

  if (commandName == 'app-name') {
    await executeRename(
      newName: newName,
      renameCommand: 'setAppName',
      actionDescription: 'app name',
    );
  } else if (commandName == 'package-name') {
    await executeRename(
      newName: newName,
      renameCommand: 'setBundleId',
      actionDescription: 'package name',
    );
  }
}

/// Interactive flow for changing the app name.
Future<void> changeAppName() async {
  printTitle('Change App Name');
  stdout.write('Enter the new app name (e.g., "My Awesome App"): ');
  final newName = stdin.readLineSync();
  if (newName == null || newName.isEmpty) {
    printError('App name cannot be empty. Aborting.');
    exit(1);
  }
  await executeRename(
    newName: newName,
    renameCommand: 'setAppName',
    actionDescription: 'app name',
  );
}

/// Interactive flow for changing the package name.
Future<void> changePackageName() async {
  printTitle('Change Package Name');
  stdout.write('Enter the new package name (e.g., com.company.app): ');
  final newName = stdin.readLineSync();
  if (newName == null || newName.isEmpty) {
    printError('Package name cannot be empty. Aborting.');
    exit(1);
  }
  await executeRename(
    newName: newName,
    renameCommand: 'setBundleId',
    actionDescription: 'package name',
  );
}

/// The core function that executes the `rename` command using the NEW API.
Future<void> executeRename({
  required String newName,
  required String renameCommand, // e.g., 'setAppName' or 'setBundleId'
  required String actionDescription, // e.g., 'app name' or 'package name'
}) async {
  print(
    '\nThis will change the ${AnsiColors.cyan}$actionDescription${AnsiColors.reset} to:',
  );
  print('${AnsiColors.magenta}"$newName"${AnsiColors.reset}');
  printWarning(
    'This is a major change. It is highly recommended to commit your code to version control before proceeding.',
  );
  stdout.write('Do you want to continue? (y/N): ');

  final confirmation = stdin.readLineSync()?.toLowerCase();
  if (confirmation != 'y') {
    print('Operation cancelled.');
    exit(0);
  }

  print('\nRunning the rename tool...');
  try {
    // *** THE KEY CHANGE IS HERE ***
    // We now build the command according to the `rename: ^3.1.0` API.
    final result = await Process.run('dart', [
      'run',
      'rename',
      renameCommand,
      '--value',
      newName,
    ], runInShell: true);

    if (result.exitCode == 0) {
      printSuccess('Successfully changed $actionDescription!');
      print('Tool output:\n${result.stdout}');
    } else {
      printError('An error occurred while changing the $actionDescription.');
      print('Exit Code: ${result.exitCode}');
      print('--- STDOUT ---\n${result.stdout}');
      print('--- STDERR ---\n${result.stderr}');
      printError(
        'Operation failed. Your project might be in an inconsistent state.',
      );
      exit(1);
    }
  } catch (e) {
    printError(
      'Failed to execute the process. Is `rename: ^3.1.0` in your dev_dependencies?',
    );
    print(e);
    exit(1);
  }
}

// --- Helper Functions for Pretty Printing ---
void printTitle(String title) {
  print('\n${AnsiColors.yellow}--- $title ---${AnsiColors.reset}');
}

void printError(String message) {
  print('${AnsiColors.red}Error: $message${AnsiColors.reset}');
}

void printSuccess(String message) {
  print('${AnsiColors.green}$message${AnsiColors.reset}');
}

void printWarning(String message) {
  print('${AnsiColors.yellow}WARNING: $message${AnsiColors.reset}');
}
