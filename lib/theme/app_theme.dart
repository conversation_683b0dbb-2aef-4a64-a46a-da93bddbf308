import 'package:flutter/material.dart';

import '../extensions/text_style_extension.dart';

class AppTheme {
  static ThemeData lightTheme(BuildContext context) {
    return ThemeData(
      fontFamily: 'SFPro',
      textTheme: TextTheme(
        displayLarge: context.titleLarge,
        displayMedium: context.titleMedium,
        displaySmall: context.titleSmall,
        bodyLarge: context.bodyLarge,
        bodyMedium: context.bodyMedium,
        bodySmall: context.bodySmall,
        labelLarge: context.labelLarge,
        labelMedium: context.labelMedium,
        labelSmall: context.labelSmall,
      ),
      // Add other theme configurations here
    );
  }

  static ThemeData darkTheme(BuildContext context) {
    return ThemeData(
      fontFamily: 'SFPro',
      brightness: Brightness.dark,
      textTheme: TextTheme(
        displayLarge: context.titleLarge.copyWith(color: Colors.white),
        displayMedium: context.titleMedium.copyWith(color: Colors.white),
        displaySmall: context.titleSmall.copyWith(color: Colors.white),
        bodyLarge: context.bodyLarge.copyWith(color: Colors.white),
        bodyMedium: context.bodyMedium.copyWith(color: Colors.white),
        bodySmall: context.bodySmall.copyWith(color: Colors.white),
        labelLarge: context.labelLarge.copyWith(color: Colors.white),
        labelMedium: context.labelMedium.copyWith(color: Colors.white),
        labelSmall: context.labelSmall.copyWith(color: Colors.white),
      ),
      // Add other theme configurations here
    );
  }
} 