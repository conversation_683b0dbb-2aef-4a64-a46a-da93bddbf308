import 'package:flutter/material.dart';

class MyColors {
  static Color _primary = hexToColor('#004c4c');
  static Color get primary => _primary;
  static set primary(Color color) {
    if (_primary == hexToColor('#004c4c')) {
      _primary = color;
    }
  }

  static final Color aquaHaze = hexToColor('#F7F9FA');
  static final Color catskillWhite = hexToColor('#ECF1F5');
  static final Color chardonnay = hexToColor('#FFCC80');
  static final Color emeraldGreen = hexToColor('#2ecc71');
  static final Color lightestBlue = hexToColor('#E0F2F7');
  static final Color lightestGreen = hexToColor('#E8F5E9');
  static final Color miska = hexToColor('#CFD4DA');
  static final Color tradewind = hexToColor('#56AB9F');
  static final Color travertine = hexToColor('#FFFDE7');
}

Color hexToColor(String hexColor) {
  hexColor = hexColor.replaceAll("#", "");
  if (hexColor.length == 6) {
    hexColor = "FF$hexColor";
  }
  return Color(int.parse(hexColor, radix: 16));
}
