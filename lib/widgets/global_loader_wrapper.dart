import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'loader.dart';

class GlobalLoaderWrapper extends ConsumerWidget {
  final Widget child;

  const GlobalLoaderWrapper({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final loaderState = ref.watch(loaderProvider);

    return Stack(
      alignment: Alignment.topLeft, // Use non-directional alignment
      children: [
        child,
        if (loaderState.canShowLoader) const Loader(),
      ],
    );
  }
}
