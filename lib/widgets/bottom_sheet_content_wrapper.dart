import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/widgets/dismiss_keyboard_wrapper.dart';
import 'package:flutter/material.dart';

class BottomSheetContentWrapper extends StatelessWidget {
  final double? heightFactor;
  final bool intrinsicContent;

  const BottomSheetContentWrapper(
      {super.key,
      this.child,
      this.heightFactor,
      this.intrinsicContent = false});

  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: DismissKeyboardWrapper(
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(context.d16),
              topRight: Radius.circular(context.d16),
            ),
          ),
          height: intrinsicContent == false
              ? MediaQuery.of(context).size.height * (heightFactor ?? 0.7) +
                  MediaQuery.of(context).viewInsets.bottom
              : null,
          padding: MediaQuery.of(context).viewInsets,
          width: double.infinity,
          child:
              intrinsicContent == true ? IntrinsicHeight(child: child) : child,
        ),
      ),
    );
  }
}
