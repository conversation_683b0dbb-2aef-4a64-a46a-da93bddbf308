import 'package:common_widgets/extensions/text_size_extension.dart'; // Added import
import 'package:common_widgets/theme/colors.dart';
import 'package:flutter/material.dart';

class AddShape extends StatelessWidget {
  const AddShape({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(context.d12),
      color: MyColors.primary,
      child: Icon(Icons.add, size: context.d32, color: Colors.white),
    );
  }
}
