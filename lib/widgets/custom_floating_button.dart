import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/theme/colors.dart';
import 'package:flutter/material.dart';

class CustomFloatingButton extends StatelessWidget {
  const CustomFloatingButton({super.key, required this.onPress, required this.icon});

  final void Function() onPress;
  final IconData icon;

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      elevation: 0,
      backgroundColor: MyColors.primary,
      focusElevation: 0,
      heroTag: null,
      shape: RoundedSuperellipseBorder(
        borderRadius: BorderRadius.circular(context.d12),
      ),
      onPressed: onPress,
      child: Icon(
        icon,
        size: context.d32,
        color: Colors.white,
      ),
    );
  }
}
