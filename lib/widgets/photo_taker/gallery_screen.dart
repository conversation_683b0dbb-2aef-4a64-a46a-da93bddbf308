import 'dart:io';

import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/widgets/custom_app_bar.dart';
import 'package:common_widgets/widgets/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';

class GalleryScreen extends StatefulWidget {
  final List<String> imageUrls;
  final int initialIndex;
  final bool useLocalFile;

  const GalleryScreen(this.imageUrls, this.initialIndex, {super.key, this.useLocalFile = false});

  @override
  State<GalleryScreen> createState() => _GalleryScreenState();
}

class _GalleryScreenState extends State<GalleryScreen> {
  int currentIndex = 0;
  PageController pageController = PageController();

  @override
  void initState() {
    super.initState();
    todo();
  }

  @override
  void didUpdateWidget(covariant GalleryScreen oldWidget) {
    todo();
    super.didUpdateWidget(oldWidget);
  }

  void todo() {
    setState(() {
      currentIndex = widget.initialIndex;
      pageController = PageController(initialPage: widget.initialIndex);
    });
  }

  void onPageChanged(int index) {
    setState(() {
      currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: 'Trình xem ảnh'),
      body: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          PhotoViewGallery.builder(
            pageController: pageController,
            itemCount: widget.imageUrls.length,
            builder: (context, index) {
              if (widget.useLocalFile == false) {
                return PhotoViewGalleryPageOptions(
                  imageProvider: NetworkImage(widget.imageUrls[index]),
                  initialScale: PhotoViewComputedScale.contained * 0.8,
                  heroAttributes: PhotoViewHeroAttributes(tag: index),
                );
              }

              return PhotoViewGalleryPageOptions(
                imageProvider: FileImage(File(widget.imageUrls[index])),
                initialScale: PhotoViewComputedScale.contained * 0.8,
                heroAttributes: PhotoViewHeroAttributes(tag: index),
              );
            },
            onPageChanged: onPageChanged,
          ),
          Container(
            padding: const EdgeInsets.all(20.0),
            color: Colors.white.withValues(alpha: 0.2),
            width: double.infinity,
            child: CustomText(
              text: "Image ${currentIndex + 1}",
              size: context.d16,
              color: Colors.white,
              textAlign: TextAlign.center,
            ),
          )
        ],
      ),
    );
    // return Scaffold(
    //   backgroundColor: Colors.black87,
    //   appBar: const CustomAppBar(
    //     title: 'Trình xem ảnh',
    //   ),
    //   body: InteractiveViewer(
    //     boundaryMargin: EdgeInsets.all(context.d16),
    //     minScale: 0.1,
    //     maxScale: 4,
    //     child: PageView.builder(
    //       itemCount: imageUrls.length,
    //       controller: PageController(initialPage: initialIndex),
    //       itemBuilder: (BuildContext context, int index) {
    //         if (useLocalFile == true) {
    //           return Image.file(
    //             File(imageUrls[index]),
    //             fit: BoxFit.contain,
    //           );
    //         }

    //         return Image.network(
    //           imageUrls[index],
    //           fit: BoxFit.contain,
    //           loadingBuilder: imageLoadingBuilder,
    //         );
    //       },
    //     ),
    //   ),
    // );
  }
}
