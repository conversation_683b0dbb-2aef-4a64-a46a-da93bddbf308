// ignore_for_file: library_private_types_in_public_api

import 'dart:io';

import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/models/photo_taker_item.dart';
import 'package:common_widgets/widgets/custom_inkwell_button.dart';
import 'package:common_widgets/widgets/image_loading_builder.dart';
import 'package:common_widgets/widgets/photo_taker/gallery_screen.dart';
import 'package:flutter/material.dart';

class PhotoTaker extends StatefulWidget {
  const PhotoTaker({
    super.key,
    this.useInSliver = false,
    this.leading,
    this.trailing,
    this.photos,
    this.onPressRemovePhoto,
    this.canRemovePhoto = true,
    this.physics,
    this.numItemPerRow = 3,
    this.removeIconSize = 24,
    this.itemBorderRadius = 16,
  });

  final List<PhotoTakerItem>? photos;
  final ScrollPhysics? physics;
  final Widget? leading;
  final Widget? trailing;
  final bool canRemovePhoto;
  final double removeIconSize;
  final double itemBorderRadius;
  final bool useInSliver;
  final int numItemPerRow;
  final void Function(int)? onPressRemovePhoto;

  @override
  _PhotoTakerState createState() => _PhotoTakerState();
}

class _PhotoTakerState extends State<PhotoTaker> {
  List<PhotoTakerItem> photos = [];

  late SliverGridDelegateWithFixedCrossAxisCount gridConfig;

  void bindParams() {
    setState(() {
      photos = widget.photos ?? [];
      gridConfig = SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.numItemPerRow,
        crossAxisSpacing: context.d16,
        mainAxisSpacing: context.d16,
      );
    });
  }

  @override
  void initState() {
    super.initState();
    bindParams();
  }

  @override
  void didUpdateWidget(PhotoTaker oldWidget) {
    super.didUpdateWidget(oldWidget);
    bindParams();
  }

  List<Widget> _bindGridItems() {
    List<Widget> gridItems = [];

    if (widget.leading != null) {
      gridItems.add(widget.leading!);
    }

    for (var i = 0, j = photos.length; i < j; i++) {
      gridItems.add(
        CustomInkwellButton(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => GalleryScreen(
                  photos.map((e) => e.path).toList(),
                  i,
                  useLocalFile: photos[i].isLocal,
                ),
              ),
            );
          },
          child: Container(
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(widget.itemBorderRadius)),
            child: Stack(
              children: [
                Positioned.fill(
                  child: photos[i].isLocal
                      ? Image.file(File(photos[i].path), fit: BoxFit.cover)
                      : Image.network(
                          photos[i].path,
                          fit: BoxFit.cover,
                          loadingBuilder: imageLoadingBuilder,
                        ),
                ),
                if (widget.canRemovePhoto && photos[i].hasDone == false)
                  Positioned(
                    top: 0,
                    right: 0,
                    child: CustomInkwellButton(
                      onTap: () {
                        if (widget.onPressRemovePhoto != null) {
                          widget.onPressRemovePhoto!(i);
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Colors.black45,
                          borderRadius: BorderRadius.circular(context.d8),
                        ),
                        child: Icon(Icons.close, size: widget.removeIconSize, color: Colors.white),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      );
    }

    if (widget.trailing != null) {
      gridItems.add(widget.trailing!);
    }

    return gridItems;
  }

  @override
  Widget build(BuildContext context) {
    final lstWidget = _bindGridItems();

    if (widget.useInSliver == true) {
      return SliverGrid.builder(
        gridDelegate: gridConfig,
        itemCount: lstWidget.length,
        itemBuilder: (context, index) {
          return lstWidget[index];
        },
      );
    }

    return GridView.builder(
      gridDelegate: gridConfig,
      physics: widget.physics,
      shrinkWrap: true,
      itemCount: lstWidget.length,
      itemBuilder: (context, index) {
        return lstWidget[index];
      },
    );
  }
}
