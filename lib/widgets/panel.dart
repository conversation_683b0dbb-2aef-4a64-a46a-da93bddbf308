import 'package:flutter/material.dart';

class Panel extends StatelessWidget {
  const Panel({super.key, this.header, this.body, this.footer, this.bodyExpand = true});

  final Widget? header;
  final Widget? body;
  final bool? bodyExpand;
  final Widget? footer;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        IntrinsicHeight(
          child: header,
        ),
        bodyExpand == true
            ? Expanded(
                child: Container(
                  child: body,
                ),
              )
            : body ?? Container(),
        IntrinsicHeight(
          child: footer,
        ),
      ],
    );
  }
}
