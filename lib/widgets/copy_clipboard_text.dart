// ignore_for_file: library_private_types_in_public_api

import 'package:common_widgets/widgets/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CopyToClipboardText extends StatefulWidget {
  final String text;
  final Color? color;
  final double? size;
  final double? wordSpacing;
  final FontWeight? fontWeight;
  final FontStyle? fontStyle;
  final TextAlign? textAlign;
  final TextOverflow? overflow;
  final TextDecoration? decoration;

  const CopyToClipboardText(
      {super.key,
      required this.text,
      this.color = Colors.black,
      this.size,
      this.wordSpacing,
      this.fontWeight,
      this.fontStyle,
      this.textAlign,
      this.overflow,
      this.decoration});

  @override
  _CopyToClipboardTextState createState() => _CopyToClipboardTextState();
}

class _CopyToClipboardTextState extends State<CopyToClipboardText> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        _copyToClipboard();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Đa sao chép: ${widget.text}'),
          ),
        );
      },
      child: CustomText(
        text: widget.text,
        size: widget.size,
        color: widget.color,
        decoration: widget.decoration,
        fontStyle: widget.fontStyle,
        fontWeight: widget.fontWeight,
        overflow: widget.overflow,
        textAlign: widget.textAlign ?? TextAlign.left,
        wordSpacing: widget.wordSpacing,
      ),
    );
  }

  Future _copyToClipboard() async {
    await Clipboard.setData(ClipboardData(text: widget.text));
  }
}
