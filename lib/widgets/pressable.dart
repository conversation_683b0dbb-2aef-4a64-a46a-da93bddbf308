import 'package:flutter/material.dart';

class Pressable extends StatelessWidget {
  final Widget child;
  final Function() onTap;
  final double radius;

  const Pressable({
    super.key,
    required this.child,
    required this.onTap,
    required this.radius,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(radius),
        child: Ink(child: child),
      ),
    );
  }
}
