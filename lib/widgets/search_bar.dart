import 'package:common_widgets/extensions/screen_extension.dart';
import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/theme/colors.dart';
import 'package:common_widgets/widgets/custom_normal_button.dart';
import 'package:common_widgets/widgets/custom_textfield.dart';
import 'package:flutter/material.dart';

class MySearchBar extends StatelessWidget {
  final void Function(String) onTextChange;
  final void Function() onPressSearch;
  final String? placeHolder;
  final String value;

  const MySearchBar({
    super.key,
    required this.onTextChange,
    required this.onPressSearch,
    this.placeHolder = 'Tìm theo từ khóa',
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        Expanded(
          child: Container(
            height: context.scaleWithValue(60),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.horizontal(
                left: Radius.circular(context.d12),
              ),
              border: Border.all(color: Colors.grey.shade300),
            ),
            padding: EdgeInsets.only(right: context.d8),
            alignment: Alignment.center,
            child: CustomTextField(
              contentPadding: EdgeInsets.symmetric(horizontal: context.d16),
              placeHolder: placeHolder!,
              onChanged: onTextChange,
              value: value,
              border: const OutlineInputBorder(borderSide: BorderSide.none),
              enabledBorder: const OutlineInputBorder(
                borderSide: BorderSide.none,
              ),
              focusBorder: const OutlineInputBorder(
                borderSide: BorderSide.none,
              ),
            ),
          ),
        ),
        Container(
          height: context.scaleWithValue(60),
          width: context.scaleWithValue(60),
          decoration: BoxDecoration(
            color: MyColors.primary,
            borderRadius: BorderRadius.horizontal(
              right: Radius.circular(context.d12),
            ),
          ),
          child: CustomNormalButton(
            onPress: onPressSearch,
            padding: p12,
            icon: Icon(Icons.search, color: Colors.white, size: context.d24),
          ),
        ),
      ],
    );
  }
}
