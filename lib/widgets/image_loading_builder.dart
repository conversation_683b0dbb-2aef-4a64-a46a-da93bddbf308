import 'package:common_widgets/theme/colors.dart';
import 'package:flutter/material.dart';

Widget imageLoadingBuilder(context, child, loadingProgress) {
  if (loadingProgress == null) {
    return child;
  }
  return Padding(
    padding: EdgeInsets.all(context.context.d16),
    child: Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation(MyColors.primary),
        value: loadingProgress.expectedTotalBytes != null ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes! : null,
      ),
    ),
  );
}
