// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final willPopScopeProvider = StateNotifierProvider<WillPopScopeNotifier, WillPopScopeState>((ref) {
  return WillPopScopeNotifier();
});

class WillPopScopeState {
  final bool canPop;

  const WillPopScopeState({required this.canPop});

  WillPopScopeState copyWith({bool? canPop}) {
    return WillPopScopeState(
      canPop: canPop ?? this.canPop,
    );
  }
}

class WillPopScopeNotifier extends StateNotifier<WillPopScopeState> {
  WillPopScopeNotifier() : super(const WillPopScopeState(canPop: true));

  void changePopStatus(bool canPop) {
    state = state.copyWith(canPop: canPop);
  }
}

class WillPopScopeWrapper extends ConsumerWidget {
  final Widget child;
  final Function()? onBack;

  const WillPopScopeWrapper({super.key, required this.child, this.onBack});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(willPopScopeProvider);

    return WillPopScope(
      onWillPop: () async {
        if (state.canPop == false) {
          return false;
        }

        if (onBack != null) {
          onBack!();
        }

        return true;
      },
      child: child,
    );
  }
}
