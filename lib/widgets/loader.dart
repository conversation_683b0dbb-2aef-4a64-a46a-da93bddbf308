import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/theme/colors.dart';
import 'package:common_widgets/widgets/custom_text.dart';
import 'package:common_widgets/widgets/will_pop_scope_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

final loaderProvider = StateNotifierProvider<LoaderNotifier, LoaderState>((
  ref,
) {
  return LoaderNotifier();
});

class LoaderState {
  final bool canShowLoader;
  final String? message;

  const LoaderState({required this.canShowLoader, this.message});

  LoaderState copyWith({required bool canShowLoader, String? message}) {
    return LoaderState(
      canShowLoader: canShowLoader,
      message: canShowLoader == true ? message : '',
    );
  }
}

class LoaderNotifier extends StateNotifier<LoaderState> {
  LoaderNotifier() : super(const LoaderState(canShowLoader: false));

  void toggleLoader({required bool canShow, String? message}) {
    state = state.copyWith(
      canShowLoader: canShow,
      message: message ?? 'Đang xử lý...',
    );
  }
}

class Loader extends ConsumerStatefulWidget {
  const Loader({
    super.key,
    this.message,
    this.showButtonGoBack = false,
    this.useExpand = true,
  });

  final String? message;
  final bool showButtonGoBack;
  final bool useExpand;

  @override
  ConsumerState<Loader> createState() => _LoaderState();
}

class _LoaderState extends ConsumerState<Loader> {
  bool canShowGoBack = false;

  @override
  void initState() {
    super.initState();
    canShowGoBack = widget.showButtonGoBack;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final state = ref.read(loaderProvider);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(willPopScopeProvider.notifier)
          .changePopStatus(!state.canShowLoader);
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(loaderProvider);

    if (state.canShowLoader == false) {
      return const SizedBox(height: 0);
    }

    return Positioned.fill(
      child: Directionality(
        textDirection: TextDirection.ltr,
        child: Container(
          color: const Color.fromRGBO(0, 0, 0, 0.5),
          child: Center(
            child: IntrinsicHeight(
              child: Container(
                padding: EdgeInsets.fromLTRB(
                  context.d16,
                  context.d32,
                  context.d16,
                  context.d32,
                ),
                constraints: BoxConstraints(maxWidth: context.d16 * 20),
                decoration: ShapeDecoration(
                  color: Colors.white,
                  shape: RoundedSuperellipseBorder(
                    borderRadius: BorderRadius.all(
                      Radius.circular(context.d16),
                    ),
                  ),
                ),
                child: Spinner(
                  message: state.message,
                  useExpand: widget.useExpand,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class Spinner extends StatelessWidget {
  final String? message;
  final bool useExpand;
  final double? height;
  final double? width;

  const Spinner({
    super.key,
    this.message,
    required this.useExpand,
    this.height,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    final Widget content = Wrap(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: context.d32),
          child: CustomText(
            text: message ?? '',
            textAlign: TextAlign.center,
            color: Colors.black,
            fontWeight: FontWeight.normal,
            size: context.d16,
          ),
        ),
      ],
    );

    return Center(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          LoadingAnimationWidget.discreteCircle(
            color: MyColors.primary,
            size: height ?? context.scaleWithValue(40),
          ),
          if (message?.isNotEmpty == true) ...[
            SizedBox(height: context.d16),
            useExpand ? Expanded(child: content) : content,
          ],
        ],
      ),
    );
  }
}
