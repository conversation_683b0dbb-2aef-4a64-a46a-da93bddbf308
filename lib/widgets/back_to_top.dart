import 'package:common_widgets/widgets/custom_floating_button.dart';
import 'package:flutter/material.dart';

class BackToTop extends StatelessWidget {
  const BackToTop({
    super.key,
    required ScrollController scrollController,
    required this.bottom,
    required this.right,
  }) : _scrollController = scrollController;

  final ScrollController _scrollController;
  final double bottom;
  final double right;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: bottom,
      right: right,
      child: CustomFloatingButton(
          onPress: () {
            _scrollController.animateTo(
              0,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          },
          icon: Icons.arrow_upward),
    );
  }
}
