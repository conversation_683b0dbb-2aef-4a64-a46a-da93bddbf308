// ignore_for_file: avoid_print

import 'package:common_widgets/init.dart';
import 'package:common_widgets/utils/router/router_util.dart';
import 'package:flutter/material.dart';

class RouterWrapper extends StatefulWidget {
  RouterWrapper({
    super.key,
    this.autoGenerateRoutes = true,
    this.directoryPath = 'lib/routes',
  }) : assert(
         !autoGenerateRoutes ||
             (autoGenerateRoutes && directoryPath.isNotEmpty),
         'directoryPath is required when autoGenerateRoutes is true',
       );

  final String directoryPath;
  final bool autoGenerateRoutes;

  @override
  State<RouterWrapper> createState() => _RouterWrapperState();
}

class _RouterWrapperState extends State<RouterWrapper> {
  @override
  void initState() {
    super.initState();
    if (widget.autoGenerateRoutes) {
      RouterUtil.updateRoutesFromDirectory(
        widget.directoryPath,
        navigatorKey: navigatorKey,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(routerConfig: RouterUtil.router);
  }
}
