import 'package:common_widgets/extensions/context_extension.dart';
import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/widgets/custom_text.dart';
import 'package:flutter/material.dart';

class ModuleIcon extends StatelessWidget {
  final double iconWidth;
  final String iconPath;
  final String name;

  const ModuleIcon({
    super.key,
    required this.iconWidth,
    required this.iconPath,
    required this.name,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // SvgPicture.asset(
          //   iconPath,
          //   width: context.scaleWithValue(iconWidth),
          // ),
          SizedBox(height: context.scaleWithValue(8)),
          CustomText(
            text: name,
            size: context.d14,
            textStyle: context.theme.titleMedium,
          )
        ],
      ),
    );
  }
}
