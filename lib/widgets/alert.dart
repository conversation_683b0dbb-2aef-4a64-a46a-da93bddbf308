import 'dart:async';

import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/extensions/text_style_extension.dart';
import 'package:common_widgets/init.dart';
import 'package:common_widgets/theme/colors.dart';
import 'package:common_widgets/widgets/custom_normal_button.dart';
import 'package:flutter/material.dart';

/// Configuration class for AlertDialog customization
class AlertDialogConfig {
  final String? title;
  final Widget? icon;
  final EdgeInsetsGeometry? iconPadding;
  final Color? iconColor;
  final EdgeInsetsGeometry? titlePadding;
  final TextStyle? titleTextStyle;
  final EdgeInsets? insetPadding;
  final EdgeInsetsGeometry? contentPadding;
  final TextStyle? contentTextStyle;
  final EdgeInsetsGeometry? actionsPadding;
  final MainAxisAlignment? actionsAlignment;
  final OverflowBarAlignment? actionsOverflowAlignment;
  final VerticalDirection? actionsOverflowDirection;
  final double? actionsOverflowButtonSpacing;
  final EdgeInsetsGeometry? buttonPadding;
  final Color? backgroundColor;
  final double? elevation;
  final Color? shadowColor;
  final Color? surfaceTintColor;
  final String? semanticLabel;
  final ShapeBorder? shape;
  final AlignmentGeometry? alignment;
  final bool? scrollable;
  final ScrollController? scrollController;
  final ScrollController? actionScrollController;
  final Duration? insetAnimationDuration;
  final Curve? insetAnimationCurve;

  const AlertDialogConfig({
    this.title,
    this.icon,
    this.iconPadding,
    this.iconColor,
    this.titlePadding,
    this.titleTextStyle,
    this.insetPadding,
    this.contentPadding,
    this.contentTextStyle,
    this.actionsPadding,
    this.actionsAlignment,
    this.actionsOverflowAlignment,
    this.actionsOverflowDirection,
    this.actionsOverflowButtonSpacing,
    this.buttonPadding,
    this.backgroundColor,
    this.elevation,
    this.shadowColor,
    this.surfaceTintColor,
    this.semanticLabel,
    this.shape,
    this.alignment,
    this.scrollable,
    this.scrollController,
    this.actionScrollController,
    this.insetAnimationDuration,
    this.insetAnimationCurve,
  });
}

/// A utility class for displaying various types of alerts and dialogs
class Alert {
  static BuildContext get _context =>
      navigatorKey.currentState!.overlay!.context;

  /// Creates a default shape for alert dialogs
  static ShapeBorder _defaultShape(BuildContext context) =>
      RoundedSuperellipseBorder(
        borderRadius: BorderRadius.circular(context.d32),
      );

  /// Creates a default title widget for alert dialogs
  static Widget _defaultTitle(BuildContext context, {String? customTitle}) =>
      Text(
        customTitle ?? 'Thông báo',
        textAlign: TextAlign.center,
        style: context.titleLarge,
      );

  /// Shows a basic alert dialog with the given message and configuration
  static Future<void> show({
    required String message,
    String? title,
    List<Widget>? actions,
    Widget? icon,
    EdgeInsetsGeometry? iconPadding,
    Color? iconColor,
    EdgeInsetsGeometry? titlePadding,
    TextStyle? titleTextStyle,
    EdgeInsets? insetPadding,
    EdgeInsetsGeometry? contentPadding,
    TextStyle? contentTextStyle,
    EdgeInsetsGeometry? actionsPadding,
    MainAxisAlignment? actionsAlignment,
    OverflowBarAlignment? actionsOverflowAlignment,
    VerticalDirection? actionsOverflowDirection,
    double? actionsOverflowButtonSpacing,
    EdgeInsetsGeometry? buttonPadding,
    Color? backgroundColor,
    double? elevation,
    Color? shadowColor,
    Color? surfaceTintColor,
    String? semanticLabel,
    ShapeBorder? shape,
    AlignmentGeometry? alignment,
    bool? scrollable,
    ScrollController? scrollController,
    ScrollController? actionScrollController,
    Duration? insetAnimationDuration,
    Curve? insetAnimationCurve,
  }) async {
    final config = AlertDialogConfig(
      title: title,
      icon: icon,
      iconPadding: iconPadding,
      iconColor: iconColor,
      titlePadding: titlePadding,
      titleTextStyle: titleTextStyle,
      insetPadding: insetPadding,
      contentPadding: contentPadding,
      contentTextStyle: contentTextStyle,
      actionsPadding: actionsPadding,
      actionsAlignment: actionsAlignment,
      actionsOverflowAlignment: actionsOverflowAlignment,
      actionsOverflowDirection: actionsOverflowDirection,
      actionsOverflowButtonSpacing: actionsOverflowButtonSpacing,
      buttonPadding: buttonPadding,
      backgroundColor: backgroundColor,
      elevation: elevation,
      shadowColor: shadowColor,
      surfaceTintColor: surfaceTintColor,
      semanticLabel: semanticLabel,
      shape: shape,
      alignment: alignment,
      scrollable: scrollable,
      scrollController: scrollController,
      actionScrollController: actionScrollController,
      insetAnimationDuration: insetAnimationDuration,
      insetAnimationCurve: insetAnimationCurve,
    );

    final defaultActions = [
      TextButton(
        onPressed: () => Navigator.of(_context).pop(),
        child: const Text('OK'),
      ),
    ];

    await _showDialog(
      message: message,
      config: config,
      actions: actions ?? defaultActions,
    );
  }

  /// Shows a dialog with custom content widget
  static Future<void> showWithContent({
    required Widget content,
    String? title,
    List<Widget>? actions,
    Widget? icon,
    EdgeInsetsGeometry? iconPadding,
    Color? iconColor,
    EdgeInsetsGeometry? titlePadding,
    TextStyle? titleTextStyle,
    EdgeInsets? insetPadding,
    EdgeInsetsGeometry? contentPadding,
    TextStyle? contentTextStyle,
    EdgeInsetsGeometry? actionsPadding,
    MainAxisAlignment? actionsAlignment,
    OverflowBarAlignment? actionsOverflowAlignment,
    VerticalDirection? actionsOverflowDirection,
    double? actionsOverflowButtonSpacing,
    EdgeInsetsGeometry? buttonPadding,
    Color? backgroundColor,
    double? elevation,
    Color? shadowColor,
    Color? surfaceTintColor,
    String? semanticLabel,
    ShapeBorder? shape,
    AlignmentGeometry? alignment,
    bool? scrollable,
    ScrollController? scrollController,
    ScrollController? actionScrollController,
    Duration? insetAnimationDuration,
    Curve? insetAnimationCurve,
  }) async {
    final config = AlertDialogConfig(
      title: title,
      icon: icon,
      iconPadding: iconPadding,
      iconColor: iconColor,
      titlePadding: titlePadding,
      titleTextStyle: titleTextStyle,
      insetPadding: insetPadding,
      contentPadding: contentPadding,
      contentTextStyle: contentTextStyle,
      actionsPadding: actionsPadding,
      actionsAlignment: actionsAlignment,
      actionsOverflowAlignment: actionsOverflowAlignment,
      actionsOverflowDirection: actionsOverflowDirection,
      actionsOverflowButtonSpacing: actionsOverflowButtonSpacing,
      buttonPadding: buttonPadding,
      backgroundColor: backgroundColor,
      elevation: elevation,
      shadowColor: shadowColor,
      surfaceTintColor: surfaceTintColor,
      semanticLabel: semanticLabel,
      shape: shape,
      alignment: alignment,
      scrollable: scrollable,
      scrollController: scrollController,
      actionScrollController: actionScrollController,
      insetAnimationDuration: insetAnimationDuration,
      insetAnimationCurve: insetAnimationCurve,
    );

    await _showDialog(content: content, config: config, actions: actions ?? []);
  }

  /// Internal method to show dialog with common configuration
  static Future<void> _showDialog({
    String? message,
    Widget? content,
    required AlertDialogConfig config,
    required List<Widget> actions,
  }) async {
    await showDialog(
      context: _context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: _defaultTitle(context, customTitle: config.title),
          insetPadding:
              config.insetPadding ??
              const EdgeInsets.symmetric(horizontal: 40.0, vertical: 24.0),
          content:
              content ??
              SingleChildScrollView(
                child: Text(
                  message!,
                  textAlign: TextAlign.center,
                  style: context.bodyMedium,
                ),
              ),
          icon: config.icon,
          iconPadding: config.iconPadding,
          iconColor: config.iconColor,
          titlePadding: config.titlePadding,
          titleTextStyle: config.titleTextStyle,
          contentPadding: config.contentPadding,
          contentTextStyle: config.contentTextStyle,
          actionsPadding: config.actionsPadding,
          actionsAlignment: config.actionsAlignment,
          actionsOverflowAlignment: config.actionsOverflowAlignment,
          actionsOverflowDirection: config.actionsOverflowDirection,
          actionsOverflowButtonSpacing: config.actionsOverflowButtonSpacing,
          buttonPadding: config.buttonPadding,
          elevation: config.elevation,
          shadowColor: config.shadowColor,
          surfaceTintColor: config.surfaceTintColor ?? Colors.white,
          semanticLabel: config.semanticLabel,
          shape: config.shape ?? _defaultShape(context),
          alignment: config.alignment,
          backgroundColor: config.backgroundColor,
          actions: actions,
        );
      },
    );
  }

  /// Shows a warning alert with amber color scheme
  static Future<void> warning({
    required String message,
    String? title,
    List<Widget>? actions,
  }) async {
    final defaultActions = [
      Padding(
        padding: const EdgeInsets.fromLTRB(32, 0, 32, 16),
        child: CustomNormalButton(
          onPress: () => Navigator.of(_context).pop(),
          backgroundColor: Colors.amber,
          padding: EdgeInsets.all(_context.d14),
          label: 'OK',
        ),
      ),
    ];

    await show(
      message: message,
      title: title,
      actions: actions ?? defaultActions,
      icon: const Icon(Icons.warning, size: 64),
      iconColor: Colors.amber,
    );
  }

  /// Shows a danger alert with red color scheme
  static Future<void> danger({
    EdgeInsets? insetPadding,
    FutureOr Function(BuildContext)? onPressDefault,
    List<Widget>? actions,
    String message = 'Thao tác không thành công',
    String? title,
    TextStyle? contentTextStyle,
    TextStyle? titleTextStyle,
  }) async {
    final defaultActions = [
      Padding(
        padding: const EdgeInsets.fromLTRB(32, 0, 32, 16),
        child: CustomNormalButton(
          onPress: () async {
            Navigator.of(_context).pop();
            await onPressDefault?.call(_context);
          },
          backgroundColor: Colors.red,
          padding: EdgeInsets.all(_context.d14),
          label: 'OK',
        ),
      ),
    ];

    await show(
      message: message,
      title: title,
      titleTextStyle: titleTextStyle,
      insetPadding: insetPadding,
      contentTextStyle: contentTextStyle,
      actions: actions ?? defaultActions,
      backgroundColor: Colors.white,
      icon: const Icon(Icons.warning, size: 64),
      iconColor: Colors.red,
    );
  }

  /// Shows a failure alert with default error message
  static Future<void> fail() async {
    return await danger(message: 'Thao tác không thành công');
  }

  /// Shows a success alert with green color scheme
  static Future<void> success({
    String? message,
    String? title,
    List<Widget>? actions,
    void Function()? onPressDefault,
    TextStyle? titleTextStyle,
    TextStyle? contentTextStyle,
  }) async {
    final defaultActions = [
      Padding(
        padding: const EdgeInsets.fromLTRB(32, 0, 32, 0),
        child: CustomNormalButton(
          onPress: () {
            Navigator.of(_context).pop();
            onPressDefault?.call();
          },
          backgroundColor: Colors.green,
          padding: EdgeInsets.all(_context.d14),
          label: 'OK',
        ),
      ),
    ];

    await show(
      message: message ?? 'Thao tác thành công',
      title: title,
      titleTextStyle: titleTextStyle,
      contentTextStyle: contentTextStyle,
      actions: actions ?? defaultActions,
      backgroundColor: Colors.white,
      icon: Icon(Icons.check_circle, size: _context.d64),
      iconColor: Colors.green,
    );
  }

  /// Shows a confirmation dialog with OK and Cancel buttons
  static Future<void> confirm({
    required String message,
    String? title,
    void Function()? onPressOk,
    void Function()? onPressCancel,
    String? textCancel,
    String? textOk,
    TextStyle? titleTextStyle,
    TextStyle? contentTextStyle,
    EdgeInsets? insetPadding,
  }) async {
    final actions = [
      Padding(
        padding: const EdgeInsets.fromLTRB(32, 0, 32, 8),
        child: CustomNormalButton(
          onPress: () {
            Navigator.of(_context).pop();
            onPressCancel?.call();
          },
          backgroundColor: MyColors.miska,
          padding: EdgeInsets.all(_context.d14),
          label: textCancel ?? 'Hủy',
          textColor: Colors.black54,
        ),
      ),
      Padding(
        padding: const EdgeInsets.fromLTRB(32, 0, 32, 0),
        child: CustomNormalButton(
          onPress: () {
            Navigator.of(_context).pop();
            onPressOk?.call();
          },
          backgroundColor: Colors.red,
          padding: EdgeInsets.all(_context.d14),
          label: textOk ?? 'OK',
        ),
      ),
    ];

    await danger(
      message: message,
      title: title,
      titleTextStyle: titleTextStyle,
      contentTextStyle: contentTextStyle,
      insetPadding: insetPadding,
      actions: actions,
    );
  }
}
