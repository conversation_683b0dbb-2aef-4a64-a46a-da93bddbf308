import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/theme/colors.dart';
import 'package:common_widgets/widgets/custom_text.dart';
import 'package:flutter/material.dart';

class CustomNormalButton extends StatelessWidget {
  const CustomNormalButton({
    super.key,
    required this.onPress,
    this.label,
    this.borderRadius,
    this.backgroundColor,
    this.textColor,
    this.padding,
    this.textSize,
    this.borderSide,
    this.useBothIconLabel,
    this.icon,
    this.height,
  });

  final BorderSide? borderSide;
  final Color? backgroundColor;
  final Color? textColor;
  final double? borderRadius;
  final double? textSize;
  final Icon? icon;
  final EdgeInsetsGeometry? padding;
  final String? label;
  final bool? useBothIconLabel;
  final double? height;
  final void Function() onPress;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(context.d8),
      ),
      child: ElevatedButton(
        onPressed: onPress,
        style: TextButton.styleFrom(
          elevation: 0,
          padding: padding ?? const EdgeInsets.all(16),
          foregroundColor: WidgetStateColor.resolveWith(
            (states) => textColor ?? Colors.white,
          ),
          backgroundColor: WidgetStateColor.resolveWith(
            (states) => backgroundColor ?? MyColors.primary,
          ),
          shape: RoundedSuperellipseBorder(
            borderRadius: BorderRadius.circular(borderRadius ?? context.d12),
            side: borderSide ?? BorderSide.none,
          ),
        ),
        child: useBothIconLabel == true
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  if (icon != null) ...[icon!, SizedBox(width: context.d8)],
                  if (label != null)
                    CustomText(
                      text: label ?? '',
                      size: textSize,
                      fontWeight: FontWeight.normal,
                      color: textColor ?? Colors.white,
                    ),
                ],
              )
            : icon ??
                  CustomText(
                    text: label ?? '',
                    size: textSize,
                    fontWeight: FontWeight.normal,
                    color: textColor ?? Colors.white,
                  ),
      ),
    );
  }
}
