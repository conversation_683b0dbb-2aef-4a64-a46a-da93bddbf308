import 'package:common_widgets/extensions/context_extension.dart';
import 'package:common_widgets/extensions/string_extension.dart';
import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/models/option_item.dart';
import 'package:common_widgets/theme/border.dart';
import 'package:common_widgets/widgets/custom_checkbox.dart';
import 'package:common_widgets/widgets/custom_normal_button.dart';
import 'package:common_widgets/widgets/custom_text.dart';
import 'package:common_widgets/widgets/custom_textfield.dart';
import 'package:common_widgets/widgets/dismiss_keyboard_wrapper.dart';
import 'package:flutter/material.dart';

class MultiDropdown extends StatefulWidget {
  final BuildContext? parentContext;
  final Function(List<OptionItem<String>>) onChanged;
  final List<OptionItem<String>> items;
  final List<OptionItem<String>> selectedValues;
  final String hint;
  final String searchHint;
  final String? errorText;
  final String? label;
  final Widget? child;
  final bool isRequired;
  final bool useAsWrapper;
  final double? height;
  final double? width;

  const MultiDropdown({
    super.key,
    required this.items,
    required this.selectedValues,
    required this.onChanged,
    this.hint = 'Select items',
    this.label,
    this.isRequired = false,
    this.errorText,
    this.searchHint = 'Search...',
    this.useAsWrapper = false,
    this.child,
    this.width,
    this.height,
    this.parentContext,
  }) : assert(
         useAsWrapper == false || child != null,
         'When useAsWrapper is true, child must be provided',
       );

  @override
  State<MultiDropdown> createState() => _MultiDropdownState();
}

class _MultiDropdownState extends State<MultiDropdown> {
  bool isExpanded = false;
  final TextEditingController _searchController = TextEditingController();
  List<OptionItem<String>> _filteredItems = [];

  @override
  void initState() {
    super.initState();
    _filteredItems = widget.items;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterItems(String query, StateSetter setDialogState) {
    setDialogState(() {
      _filteredItems = widget.items
          .where((item) => item.unsignedKey.contains(query.toSlug(separator: '')))
          .toList();
    });
  }

  void _showDialog() {
    _searchController.text = '';
    _filteredItems = widget.items;

    showDialog(
      context: widget.parentContext ?? context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return DismissKeyboardWrapper(
          child: StatefulBuilder(
            builder: (context, setDialogState) {
              return AlertDialog(
                title: Container(
                  padding: EdgeInsets.fromLTRB(context.d16, 0, context.d16, context.d16),
                  child: CustomText(
                    text: widget.label ?? '',
                    textStyle: context.theme.titleMedium,
                    textAlign: TextAlign.center,
                  ),
                ),
                shape: RoundedSuperellipseBorder(borderRadius: BorderRadius.circular(context.d12)),
                backgroundColor: Colors.white,
                contentPadding: EdgeInsets.zero,
                content: Container(
                  width: widget.width ?? MediaQuery.of(dialogContext).size.width * 1,
                  height: widget.height ?? MediaQuery.of(dialogContext).size.height * 0.6,
                  decoration: BoxDecoration(
                    border: Border(top: oneWidthCaskill, bottom: oneWidthCaskill),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Padding(
                        padding: EdgeInsets.all(context.d16),
                        child: CustomTextField(
                          controller: _searchController,
                          placeHolder: widget.searchHint,
                          prefixIcon: const Icon(Icons.search),
                          borderRadius: context.d8,
                          contentPadding: EdgeInsets.all(context.d16),
                          onChanged: (value) => _filterItems(value, setDialogState),
                        ),
                      ),
                      Expanded(
                        child: ListView.builder(
                          shrinkWrap: true,
                          itemCount: _filteredItems.length,
                          itemBuilder: (context, index) {
                            final item = _filteredItems[index];
                            final isSelected = widget.selectedValues.any(
                              (e) => e.value == item.value,
                            );
                            return Padding(
                              padding: EdgeInsets.only(bottom: context.d8),
                              child: CustomCheckbox(
                                label: item.key,
                                initValue: isSelected,
                                scale: 1.2,
                                onChange: (bool? value) {
                                  final newSelectedValues = List<OptionItem<String>>.from(
                                    widget.selectedValues,
                                  );
                                  if (value == true) {
                                    newSelectedValues.add(item);
                                  } else {
                                    newSelectedValues.removeWhere((e) => e.value == item.value);
                                  }
                                  widget.onChanged(newSelectedValues);
                                  setDialogState(() {}); // Update dialog state after selection
                                },
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                actionsPadding: EdgeInsets.all(context.d16),
                actions: [
                  CustomNormalButton(
                    onPress: () {
                      Navigator.of(dialogContext).pop();
                    },
                    label: 'OK',
                  ),
                ],
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildDropdownContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Row(
            children: [
              CustomText(text: widget.label!),
              if (widget.isRequired)
                const Text(' *', style: TextStyle(color: Colors.red, fontSize: 16)),
            ],
          ),
          const SizedBox(height: 8),
        ],
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: widget.errorText != null ? Colors.red : Colors.grey),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              InkWell(
                onTap: () {
                  setState(() {
                    isExpanded = !isExpanded;
                    if (!isExpanded) {
                      _searchController.clear();
                      _filteredItems = widget.items;
                    }
                  });
                },
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: context.d12, vertical: context.d8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          widget.selectedValues.isEmpty
                              ? widget.hint
                              : '${widget.selectedValues.length} items selected',
                          style: TextStyle(
                            color: widget.selectedValues.isEmpty ? Colors.grey : Colors.black,
                          ),
                        ),
                      ),
                      Icon(isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down),
                    ],
                  ),
                ),
              ),
              if (isExpanded) ...[
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: context.d12, vertical: context.d8),
                  child: CustomTextField(
                    controller: _searchController,
                    placeHolder: widget.searchHint,
                    prefixIcon: const Icon(Icons.search),
                    borderRadius: context.d8,
                    contentPadding: EdgeInsets.symmetric(vertical: context.d8),
                    onChanged: (value) => _filterItems(value, setState),
                  ),
                ),
                Container(
                  constraints: const BoxConstraints(maxHeight: 200),
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: _filteredItems.length,
                    itemBuilder: (context, index) {
                      final item = _filteredItems[index];
                      final isSelected = widget.selectedValues.contains(item);
                      return CheckboxListTile(
                        title: Text(item.key),
                        value: isSelected,
                        onChanged: (bool? value) {
                          final newSelectedValues = List<OptionItem<String>>.from(
                            widget.selectedValues,
                          );
                          if (value == true) {
                            newSelectedValues.add(item);
                          } else {
                            newSelectedValues.remove(item);
                          }
                          widget.onChanged(newSelectedValues);
                        },
                      );
                    },
                  ),
                ),
              ],
            ],
          ),
        ),
        if (widget.errorText != null)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(widget.errorText!, style: const TextStyle(color: Colors.red, fontSize: 12)),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.useAsWrapper) {
      return GestureDetector(onTap: _showDialog, child: widget.child);
    }
    return _buildDropdownContent();
  }
}
