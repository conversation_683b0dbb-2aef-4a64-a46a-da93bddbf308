import 'package:common_widgets/utils/navigation_util.dart';
import 'package:flutter/material.dart';

class CustomBackButton extends StatelessWidget {
  final Color? iconColor;
  final Function()? onBack;

  const CustomBackButton({super.key, this.iconColor, this.onBack});

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: onBack ?? () => back(),
      icon: Icon(Icons.chevron_left, color: iconColor ?? Colors.black),
    );
  }
}
