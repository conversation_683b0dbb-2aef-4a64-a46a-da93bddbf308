import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/theme/border.dart';
import 'package:common_widgets/theme/colors.dart';
import 'package:flutter/material.dart';

class CustomBottomNavBar extends StatelessWidget {
  const CustomBottomNavBar({
    super.key,
    required this.onTap,
    required this.selectedIndex,
    required this.items,
  });

  final void Function(int) onTap;
  final int selectedIndex;
  final List<BottomNavigationBarItem> items;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(border: Border(top: oneWidthCaskill)),
      child: BottomNavigationBar(
        currentIndex: selectedIndex,
        backgroundColor: Colors.white,
        selectedItemColor: MyColors.primary,
        iconSize: context.d32,
        selectedLabelStyle: TextStyle(fontSize: context.d14, fontWeight: FontWeight.w500),
        unselectedLabelStyle: TextStyle(fontSize: context.d14, fontWeight: FontWeight.w500),
        selectedIconTheme: IconThemeData(size: context.d32),
        onTap: onTap,
        items: items,
      ),
    );
  }
}
