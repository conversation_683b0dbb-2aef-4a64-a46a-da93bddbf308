import 'package:common_widgets/widgets/global_loader_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../utils/loader_util.dart';

class AppInitializer extends ConsumerStatefulWidget {
  final Widget child;

  const AppInitializer({super.key, required this.child});

  @override
  ConsumerState<AppInitializer> createState() => _AppInitializerState();
}

class _AppInitializerState extends ConsumerState<AppInitializer> {
  @override
  void initState() {
    super.initState();
    // Initialize LoaderUtils with the ref after the first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      LoaderUtils.initialize(ref);
    });
  }

  @override
  Widget build(BuildContext context) {
    return ProviderScope(
      child: GlobalLoaderWrapper(child: Safe<PERSON><PERSON>(child: widget.child)),
    );
  }
}
