// ignore_for_file: library_private_types_in_public_api

import 'package:common_widgets/extensions/datetime_extension.dart';
import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/theme/colors.dart';
import 'package:common_widgets/widgets/custom_inkwell_button.dart';
import 'package:common_widgets/widgets/custom_text.dart';
import 'package:flutter/material.dart';

class CustomDatePicker extends StatefulWidget {
  final bool centerTitle;
  final bool readOnly;
  final BoxDecoration? decoration;
  final BuildContext parentContext;
  final DateTime? firstDate;
  final DateTime? initDate;
  final DateTime? lastDate;
  final double? borderRadius;
  final Function(DateTime) onChangeDate;

  const CustomDatePicker({
    super.key,
    required this.parentContext,
    required this.onChangeDate,
    this.initDate,
    this.borderRadius,
    this.readOnly = false,
    this.decoration,
    this.centerTitle = false,
    this.firstDate,
    this.lastDate,
  });

  @override
  _CustomDatePickerState createState() => _CustomDatePickerState();
}

class _CustomDatePickerState extends State<CustomDatePicker> {
  DateTime selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    if (widget.initDate != null) {
      setState(() {
        selectedDate = widget.initDate!;
      });
    }
  }

  @override
  void didUpdateWidget(CustomDatePicker oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initDate != null) {
      setState(() {
        selectedDate = widget.initDate!;
      });
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime now = DateTime.now();

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      initialEntryMode: DatePickerEntryMode.calendarOnly,
      firstDate: widget.firstDate ?? DateTime(now.year - 5),
      lastDate: widget.lastDate ?? DateTime(now.year + 5),
      locale: const Locale("vi", "VN"),
      builder: (context, child) {
        return Theme(
          data: ThemeData.light().copyWith(
            primaryColor: MyColors.primary, // Màu chủ đạo của DatePicker
            hintColor: MyColors.primary, // Màu sắc các điểm accent
            colorScheme: ColorScheme.light(
              primary: MyColors.primary,
            ), // Cảm nhận màu sắc
            buttonTheme: const ButtonThemeData(
              textTheme: ButtonTextTheme.primary,
            ),
          ),
          child: child ?? Container(),
        );
      },
    );
    if (picked != null && picked != selectedDate) {
      setState(() {
        selectedDate = picked;
      });

      widget.onChangeDate(selectedDate);
    }
  }

  @override
  Widget build(BuildContext context) {
    String formatedSelectedDate = selectedDate.toLocalFormat();

    return CustomInkwellButton(
      onTap: () {
        if (widget.readOnly == true) {
          return;
        }
        _selectDate(widget.parentContext);
      },
      child: Container(
        width: double.infinity,
        padding:
            EdgeInsets.symmetric(horizontal: context.d8, vertical: context.d16),
        decoration: widget.decoration ??
            BoxDecoration(
              color: widget.readOnly == true
                  ? MyColors.catskillWhite
                  : Colors.white,
              border: Border.all(
                color: MyColors.miska,
                width: 1,
              ),
              borderRadius: BorderRadius.circular(
                widget.borderRadius ?? context.d12,
              ),
            ),
        child: CustomText(
          text: formatedSelectedDate,
          size: context.d16,
          textAlign:
              widget.centerTitle == true ? TextAlign.center : TextAlign.left,
        ),
      ),
    );
  }
}
