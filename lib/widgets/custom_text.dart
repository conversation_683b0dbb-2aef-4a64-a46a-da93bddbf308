import 'package:common_widgets/extensions/text_style_extension.dart';
import 'package:flutter/material.dart';

class CustomText extends StatelessWidget {
  final Color? color;
  final FontStyle? fontStyle;
  final FontWeight? fontWeight;
  final String text;
  final TextAlign? textAlign;
  final TextDecoration? decoration;
  final TextOverflow? overflow;
  final TextStyle? textStyle;
  final VoidCallback? onClick;
  final double? size;
  final double? wordSpacing;
  final int? maxLines;
  final Widget? append;
  final Widget? prepend;
  final bool canWrap;
  final CrossAxisAlignment? crossAxisAlign;

  const CustomText({
    super.key,
    required this.text,
    this.append,
    this.prepend,
    this.color,
    this.decoration,
    this.fontStyle,
    this.fontWeight,
    this.maxLines,
    this.onClick,
    this.overflow,
    this.size,
    this.textAlign,
    this.textStyle,
    this.wordSpacing,
    this.crossAxisAlign,
    this.canWrap = false,
  });

  @override
  Widget build(BuildContext context) {
    Widget widText = Text(
      text,
      textAlign: textAlign,
      maxLines: maxLines,
      style:
          textStyle?.copyWith(
            fontSize: size,
            fontWeight: fontWeight,
            fontStyle: fontStyle,
            color: color ?? Colors.black,
            wordSpacing: wordSpacing,
            overflow: overflow,
            decoration: TextDecoration.none,
          ) ??
          context.bodyMedium.copyWith(
            fontSize: size,
            fontWeight: fontWeight,
            fontStyle: fontStyle,
            color: color,
            wordSpacing: wordSpacing,
            overflow: overflow,
            decoration: TextDecoration.none,
          ),
    );

    if (canWrap == true) {
      widText = Expanded(child: widText);
    }

    if (prepend != null && append != null) {
      return Row(
        mainAxisAlignment: textAlign == TextAlign.center
            ? MainAxisAlignment.center
            : MainAxisAlignment.start,
        crossAxisAlignment: crossAxisAlign ?? CrossAxisAlignment.center,
        children: <Widget>[prepend!, widText, append!],
      );
    } else if (append != null) {
      return Row(
        crossAxisAlignment: crossAxisAlign ?? CrossAxisAlignment.center,
        mainAxisAlignment: textAlign == TextAlign.center
            ? MainAxisAlignment.center
            : MainAxisAlignment.start,
        children: <Widget>[widText, append!],
      );
    } else if (prepend != null) {
      return Row(
        mainAxisAlignment: textAlign == TextAlign.center
            ? MainAxisAlignment.center
            : MainAxisAlignment.start,
        crossAxisAlignment: crossAxisAlign ?? CrossAxisAlignment.center,
        children: <Widget>[prepend!, widText],
      );
    }

    return widText;
  }
}
