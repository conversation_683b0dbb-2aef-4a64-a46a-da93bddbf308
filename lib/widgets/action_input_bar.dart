import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/theme/colors.dart';
import 'package:common_widgets/widgets/custom_normal_button.dart';
import 'package:flutter/material.dart';

/// A highly composable input bar that combines a flexible input area
/// with a trailing action button.
///
/// It's designed to look like a standard text field but can contain
/// any widget in its input area, not just a TextField.
class ActionInputBar extends StatelessWidget {
  /// The main widget to display in the input area.
  /// This is typically a [TextField] or a [DropdownButtonFormField],
  /// but can be any widget.
  final Widget? inputField;

  /// The callback that is executed when the trailing action button is pressed.
  final VoidCallback onActionPressed;

  /// The widget to display as the action button's icon.
  /// Defaults to a search icon.
  final Widget actionIcon;

  /// The hint text to display in the decoration.
  final String? hintText;

  /// Allows for advanced customization of the bar's appearance,
  /// such as borders, colors, and padding.
  final InputDecoration? decoration;

  /// Allows for advanced customization of the input field.
  /// The height is the height of the input field.
  final Widget Function(BuildContext context, double height)? buildInputField;

  /// The height of the input field.
  final double? height;

  const ActionInputBar({
    super.key,
    required this.onActionPressed,
    this.actionIcon = const Icon(Icons.search),
    this.buildInputField,
    this.decoration,
    this.hintText,
    this.inputField,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    final double height = context.scaleWithValue(this.height ?? 50);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        Expanded(
          child: Container(
            height: height,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.horizontal(left: Radius.circular(context.d12)),
              border: Border.all(color: Colors.grey.shade300),
            ),
            padding: EdgeInsets.only(right: context.d8),
            alignment: Alignment.center,
            child: buildInputField != null ? buildInputField!(context, height) : inputField,
          ),
        ),
        Container(
          height: height,
          width: height,
          decoration: BoxDecoration(
            color: MyColors.primary,
            borderRadius: BorderRadius.horizontal(right: Radius.circular(context.d12)),
          ),
          child: CustomNormalButton(
            onPress: onActionPressed,
            padding: EdgeInsets.all(context.d12),
            icon: Icon(Icons.search, color: Colors.white, size: context.d24),
          ),
        ),
      ],
    );
  }
}
