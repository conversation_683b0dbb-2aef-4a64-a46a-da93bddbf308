// ignore_for_file: library_private_types_in_public_api

import 'package:common_widgets/extensions/datetime_extension.dart';
import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/extensions/text_style_extension.dart';
import 'package:common_widgets/theme/border.dart';
import 'package:common_widgets/theme/colors.dart';
import 'package:common_widgets/widgets/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:month_picker_dialog/month_picker_dialog.dart';

class CustomMonthYearPicker extends StatefulWidget {
  const CustomMonthYearPicker({
    super.key,
    this.child,
    required this.onSelectedDate,
    this.firstDate,
    this.lastDate,
    this.initialDate,
    this.borderRadius,
    this.useSize = false,
    this.size,
    this.decoration,
  }) : assert(
         !useSize || (useSize && size != null),
         "Vui lòng khai báo size nếu bạn bật useSize = true",
       );

  final Widget? child;
  final void Function(DateTime) onSelectedDate;
  final DateTime? firstDate;
  final DateTime? lastDate;
  final DateTime? initialDate;
  final double? borderRadius;
  final Decoration? decoration;

  ///Nếu là true thì phải khai báo [size], dùng để chỉ định height và width cho Container
  final bool useSize;
  final Size? size;

  @override
  State<CustomMonthYearPicker> createState() => _CustomMonthYearPickerState();
}

class _CustomMonthYearPickerState extends State<CustomMonthYearPicker> {
  DateTime now = DateTime.now();

  @override
  void didUpdateWidget(CustomMonthYearPicker oldWidget) {
    setState(() {
      if (widget.initialDate != null) {
        now = widget.initialDate!;
      }
    });
    super.didUpdateWidget(oldWidget);
  }

  @override
  void initState() {
    super.initState();
    setState(() {
      if (widget.initialDate != null) {
        now = widget.initialDate!;
      }
    });
  }

  Future<void> openMonthYearPicker({required BuildContext context}) async {
    final selectedDate = await showMonthPicker(
      context: context,
      firstDate: widget.firstDate,
      initialDate: widget.initialDate ?? DateTime.now(),
      lastDate: widget.lastDate,
      monthPickerDialogSettings: MonthPickerDialogSettings(
        headerSettings: PickerHeaderSettings(
          headerBackgroundColor: MyColors.primary,
        ),
        actionBarSettings: PickerActionBarSettings(
          actionBarPadding: EdgeInsets.all(context.d16),
          cancelWidget: Container(
            padding: EdgeInsets.all(context.d16),
            decoration: BoxDecoration(
              color: MyColors.miska,
              borderRadius: BorderRadius.circular(context.d12),
            ),
            child: CustomText(
              text: 'Hủy',
              size: context.d16,
              color: Colors.black,
            ),
          ),
          confirmWidget: Container(
            padding: EdgeInsets.all(context.d16),
            decoration: BoxDecoration(
              color: MyColors.primary,
              borderRadius: BorderRadius.circular(context.d12),
            ),
            child: CustomText(
              text: 'OK',
              size: context.d16,
              color: Colors.white,
            ),
          ),
        ),
        dateButtonsSettings: PickerDateButtonsSettings(
          selectedMonthBackgroundColor: MyColors.primary,
          unselectedMonthsTextColor: Colors.black,
        ),
        dialogSettings: const PickerDialogSettings(
          blockScrolling: true,
          locale: Locale('vi', 'VN'),
        ),
      ),
    );

    if (selectedDate == null) {
      return;
    }

    setState(() {
      now = selectedDate;
    });

    widget.onSelectedDate(selectedDate);
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        await openMonthYearPicker(context: context);
      },
      child:
          widget.child ??
          Container(
            alignment: Alignment.centerLeft,
            height: widget.useSize == true ? widget.size?.height : null,
            width: (widget.useSize == true && widget.size != null)
                ? widget.size!.width
                : double.infinity,
            padding: EdgeInsets.all(context.d16),
            decoration:
                widget.decoration ??
                BoxDecoration(
                  color: Colors.white,
                  border: Border.fromBorderSide(oneWidthMiska),
                  borderRadius: BorderRadius.circular(
                    widget.borderRadius ?? context.d16,
                  ),
                ),
            child: CustomText(
              text: now.toLocalFormat(format: 'MM/yyyy'),
              textStyle: context.bodyMedium,
            ),
          ),
    );
  }
}
