import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/models/option_item.dart';
import 'package:common_widgets/widgets/custom_text.dart';
import 'package:common_widgets/widgets/list_picker.dart';
import 'package:flutter/material.dart';

class ListPickerWithFilterInput extends StatefulWidget {
  const ListPickerWithFilterInput({
    super.key,
    required this.listPickerData,
    required this.listPickerTitle,
    required this.listPickerValue,
    required this.listPickerWidth,
    required this.onSelectItem,
    required this.title,
    this.titleColor,
  });

  final Color? titleColor;
  final double listPickerWidth;
  final List<OptionItem<String>> listPickerData;
  final String listPickerTitle;
  final String listPickerValue;
  final String title;
  final void Function(OptionItem<String>) onSelectItem;

  @override
  State<ListPickerWithFilterInput> createState() => _FilterInputState();
}

class _FilterInputState extends State<ListPickerWithFilterInput> {
  @override
  Widget build(BuildContext context) {
    return Row(
      children: <Widget>[
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            if (widget.title.isNotEmpty == true) ...[
              CustomText(
                text: widget.title,
                size: context.d14,
                fontWeight: FontWeight.w500,
                color: widget.titleColor,
              ),
              SizedBox(height: context.d8),
            ],
            SizedBox(
              width: widget.listPickerWidth,
              child: ListPicker(
                context: context,
                onSelectItem: widget.onSelectItem,
                title: widget.listPickerTitle,
                data: widget.listPickerData,
                value: widget.listPickerValue,
                placeHolder: widget.listPickerTitle,
              ),
            )
          ],
        )
      ],
    );
  }
}
