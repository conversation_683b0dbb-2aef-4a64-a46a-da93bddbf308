import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/widgets/back_to_top.dart';
import 'package:flutter/material.dart';

class BackTopTopWrapper extends StatefulWidget {
  const BackTopTopWrapper({
    super.key,
    required this.child,
    required this.controller,
    this.bottomOffset = 30,
  });

  final Widget child;
  final ScrollController controller;
  final int bottomOffset;

  @override
  _BackTopTopWrapperState createState() => _BackTopTopWrapperState();
}

class _BackTopTopWrapperState extends State<BackTopTopWrapper> {
  bool _showBackToTop = false;

  void _scrollListener() {
    if (widget.controller.offset >= 100 && !_showBackToTop) {
      setState(() {
        _showBackToTop = true;
      });
    } else if (widget.controller.offset < 100 && _showBackToTop) {
      setState(() {
        _showBackToTop = false;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_scrollListener);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        if (_showBackToTop == true)
          BackToTop(
            scrollController: widget.controller,
            bottom: widget.bottomOffset.toDouble(),
            right: context.d16,
          )
      ],
    );
  }
}
