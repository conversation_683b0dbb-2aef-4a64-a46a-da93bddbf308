// ignore_for_file: library_private_types_in_public_api

import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/theme/colors.dart';
import 'package:common_widgets/widgets/custom_text.dart';
import 'package:flutter/material.dart';

class CustomCheckbox extends StatefulWidget {
  const CustomCheckbox(
      {super.key,
      this.scale,
      required this.initValue,
      required this.onChange,
      this.labelColor,
      this.label,
      this.center = false});

  final bool initValue;
  final bool center;
  final Color? labelColor;
  final double? scale;
  final String? label;
  final void Function(bool) onChange;

  @override
  _CustomCheckboxState createState() => _CustomCheckboxState();
}

class _CustomCheckboxState extends State<CustomCheckbox> {
  bool checked = false;

  void _onChange(bool isChecked) {
    setState(() {
      checked = isChecked;
    });

    widget.onChange(checked);
  }

  @override
  void initState() {
    super.initState();

    checked = widget.initValue;
  }

  @override
  void didUpdateWidget(covariant CustomCheckbox oldWidget) {
    super.didUpdateWidget(oldWidget);

    setState(() {
      checked = widget.initValue;
    });
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        _onChange(!checked);
      },
      child: Row(
        mainAxisAlignment: widget.center == true
            ? MainAxisAlignment.center
            : MainAxisAlignment.start,
        children: [
          Transform.scale(
            scale: widget.scale ?? 1.0,
            child: Checkbox(
              value: checked,
              activeColor: MyColors.primary,
              shape: RoundedSuperellipseBorder(
                  borderRadius: BorderRadius.circular(4)),
              materialTapTargetSize: MaterialTapTargetSize.padded,
              side: WidgetStateBorderSide.resolveWith(
                (states) => BorderSide(width: 1.0, color: MyColors.primary),
              ),
              onChanged: (value) {
                _onChange(!checked);
              },
            ),
          ),
          if (widget.label != null)
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(right: context.d16),
                child: CustomText(
                  text: widget.label ?? '',
                  size: context.d16,
                  color: widget.labelColor ?? Colors.black,
                ),
              ),
            )
        ],
      ),
    );
  }
}
