import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/widgets/custom_text.dart';
import 'package:flutter/material.dart';

class NoResult extends StatelessWidget {
  final IconData? icon;
  final String? content;

  const NoResult({
    super.key,
    this.icon,
    this.content,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon ?? Icons.data_object,
            color: Colors.black45,
            size: context.scaleWithValue(48),
          ),
          SizedBox(height: context.d16),
          CustomText(
            text: content ?? 'Không có dữ liệu',
            size: context.d16,
            color: Colors.black45,
          )
        ],
      ),
    );
  }
}

class NoResultWrapper extends StatelessWidget {
  final int length;
  final String? message;
  final Widget child;

  const NoResultWrapper({
    super.key,
    required this.length,
    required this.child,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    if (length == 0) {
      return NoResult(content: message);
    }
    return child;
  }
}
