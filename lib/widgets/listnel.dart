import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/models/option_item.dart';
import 'package:common_widgets/theme/colors.dart';
import 'package:common_widgets/widgets/copy_clipboard_text.dart';
import 'package:common_widgets/widgets/custom_text.dart';
import 'package:flutter/material.dart';

class Listnel extends StatelessWidget {
  const Listnel({super.key, required this.items, this.itemBuilder, this.canScroll});

  final List<OptionItem<String>> items;
  final Widget Function(int index, OptionItem<String> item)? itemBuilder;
  final bool? canScroll;

  @override
  Widget build(BuildContext context) {
    int total = items.length;
    return Container(
      decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(context.d16))),
      child: GridView.count(
          physics: (canScroll == null || canScroll == false) ? const ScrollPhysics() : const NeverScrollableScrollPhysics(),
          crossAxisCount: 1,
          childAspectRatio: context.scaleWithValue(6),
          shrinkWrap: true,
          children: List.generate(total, (index) {
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: context.d8),
              child: Container(
                decoration: BoxDecoration(
                    border: (total > 1 && index < total - 1) ? Border(bottom: BorderSide(color: MyColors.catskillWhite, width: 1)) : null),
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: context.d8),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: itemBuilder == null
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Container(
                                constraints: BoxConstraints(maxWidth: context.scaleWithValue(250)),
                                child: Wrap(
                                  children: [
                                    CustomText(
                                      text: items[index].key,
                                      size: context.d16,
                                      color: Colors.black,
                                      fontWeight: FontWeight.normal,
                                    ),
                                  ],
                                ),
                              ),
                              items[index].extraData == true //* extraData == true -> sử dụng CopyToClipboardText
                                  ? Container(
                                      constraints: BoxConstraints(maxWidth: context.scaleWithValue(150)),
                                      child: CopyToClipboardText(
                                        text: items[index].value,
                                        size: context.d16,
                                        color: Colors.black,
                                        fontWeight: FontWeight.w600,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    )
                                  : CustomText(
                                      text: items[index].value,
                                      size: context.d16,
                                      color: Colors.black,
                                      fontWeight: FontWeight.w600,
                                      overflow: TextOverflow.ellipsis,
                                    )
                            ],
                          )
                        : itemBuilder!(index, items[index]),
                  ),
                ),
              ),
            );
          })),
    );
  }
}
