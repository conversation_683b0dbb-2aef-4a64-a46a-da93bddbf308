// ignore_for_file: library_private_types_in_public_api

import 'package:common_widgets/extensions/consumer_state_extension.dart';
import 'package:common_widgets/extensions/screen_extension.dart';
import 'package:common_widgets/extensions/string_extension.dart';
import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/extensions/text_style_extension.dart';
import 'package:common_widgets/models/option_item.dart';
import 'package:common_widgets/theme/border.dart';
import 'package:common_widgets/theme/colors.dart';
import 'package:common_widgets/utils/navigation_util.dart';
import 'package:common_widgets/widgets/custom_inkwell_button.dart';
import 'package:common_widgets/widgets/custom_text.dart';
import 'package:common_widgets/widgets/custom_textfield.dart';
import 'package:common_widgets/widgets/dismiss_keyboard_wrapper.dart';
import 'package:common_widgets/widgets/panel.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final listPickerProvider =
    StateNotifierProvider.family<
      ListPickerNotifier,
      ListPickerState,
      List<OptionItem<String>>
    >((ref, initialData) {
      return ListPickerNotifier(initialData);
    });

class ListPicker extends ConsumerStatefulWidget {
  final bool alwaysShowLabel;
  final bool useAsWrapper;
  final bool? fullWidth;
  final bool? readOnly;
  final BoxBorder? border;
  final BuildContext context;
  final double? height;
  final List<OptionItem<String>> data;
  final String placeHolder;
  final String title;
  final String value;
  final String? label;
  final void Function(OptionItem<String>) onSelectItem;
  final Widget Function(OptionItem<String>)? onRenderItem;
  final Widget? child;
  final Decoration? decoration;

  const ListPicker({
    super.key,
    required this.data,
    required this.onSelectItem,
    required this.placeHolder,
    required this.title,
    required this.value,
    required this.context,
    this.onRenderItem,
    this.label,
    this.border,
    this.fullWidth,
    this.useAsWrapper = false,
    this.alwaysShowLabel = false,
    this.child,
    this.readOnly,
    this.height,
    this.decoration,
  }) : assert(
         useAsWrapper == false || child != null,
         'Vui lòng kiểm tra useAsWrapper == true, thì bạn phải khai báo cho child',
       );

  @override
  ConsumerState<ListPicker> createState() => _ListPickerState();
}

class ListPickerNotifier extends StateNotifier<ListPickerState> {
  ListPickerNotifier(List<OptionItem<String>> initialData)
    : super(const ListPickerState(lst: [], displayLst: [], keyword: '')) {
    init(initialData);
  }

  void changeKeyword(String keyword) {
    final List<OptionItem<String>> displayList = state.lst
        .where(
          (element) => (element.extraData as String).contains(
            keyword.toSlug(separator: ''),
          ),
        )
        .toList();

    state = state.copyWith(keyword: keyword, displayLst: displayList);
  }

  void init(List<OptionItem<String>> data) {
    final List<OptionItem<String>> displayList = [];
    final List<OptionItem<String>> lst = [];

    if (data.isNotEmpty) {
      for (int i = 0, j = data.length; i < j; i++) {
        String content = data[i].key;

        if (data[i].extraData2 is String && data[i].extraData2 != null) {
          content = content + data[i].extraData2!;
        }

        final item = OptionItem<String>(
          key: data[i].key,
          value: data[i].value,
          extraData: content.toSlug(separator: ''),
          extraData2: data[i].extraData2,
        );

        displayList.add(item);
        lst.add(item);
      }
    }

    state = state.copyWith(displayLst: displayList, lst: lst);
  }
}

class ListPickerState {
  final List<OptionItem<String>> lst;
  final List<OptionItem<String>> displayLst;
  final String keyword;

  const ListPickerState({
    required this.lst,
    required this.displayLst,
    required this.keyword,
  });

  ListPickerState copyWith({
    List<OptionItem<String>>? lst,
    List<OptionItem<String>>? displayLst,
    String? keyword,
  }) {
    return ListPickerState(
      lst: lst ?? this.lst,
      displayLst: displayLst ?? this.displayLst,
      keyword: keyword ?? this.keyword,
    );
  }
}

class _ListPickerState extends ConsumerState<ListPicker> {
  final TextEditingController _controller = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final selectedItem = widget.data
        .where((element) => element.value == widget.value)
        .singleOrNull;

    return CustomInkwellButton(
      onTap: () {
        if (widget.readOnly == true) {
          return;
        }
        _showDialog();
      },
      child: widget.useAsWrapper
          ? widget.child!
          : Container(
              width: widget.fullWidth == true ? double.infinity : null,
              height: widget.height,
              padding: widget.height == null ? p16 : null,
              decoration: _buildDecoration(context),
              child: widget.height == null
                  ? _buildWidgetWithHeight(selectedItem, context)
                  : _buildWidgetWithoutHeight(context, selectedItem),
            ),
    );
  }

  @override
  void didUpdateWidget(covariant ListPicker oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    safeInitState(() {
      ref.read(listPickerProvider(widget.data).notifier).init(widget.data);
    });
  }

  Decoration _buildDecoration(BuildContext context) {
    return widget.decoration ??
        ShapeDecoration(
          shape: RoundedSuperellipseBorder(
            borderRadius: BorderRadius.circular(context.d12),
            side: oneWidthMiska,
          ),
          color: widget.readOnly == true ? Colors.grey.shade200 : Colors.white,
        );
  }

  Row _buildWidgetWithHeight(
    OptionItem<String>? selectedItem,
    BuildContext context,
  ) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.label != null &&
                  (widget.alwaysShowLabel || selectedItem != null))
                CustomText(text: widget.label!, color: Colors.grey),
              if (widget.label != null &&
                  (widget.alwaysShowLabel || selectedItem != null))
                SizedBox(height: context.scaleWithValue(4)),
              CustomText(
                text: selectedItem?.key ?? widget.placeHolder,
                color: selectedItem != null ? Colors.black : Colors.grey,
              ),
            ],
          ),
        ),
        Icon(Icons.arrow_drop_down, size: context.d24, color: Colors.grey),
      ],
    );
  }

  Padding _buildWidgetWithoutHeight(
    BuildContext context,
    OptionItem<String>? selectedItem,
  ) {
    return Padding(
      padding: p16,
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (widget.label != null &&
                    (widget.alwaysShowLabel || selectedItem != null))
                  CustomText(text: widget.label!, color: Colors.grey),
                if (widget.label != null &&
                    (widget.alwaysShowLabel || selectedItem != null))
                  SizedBox(height: context.scaleWithValue(4)),
                CustomText(
                  text: selectedItem?.key ?? widget.placeHolder,
                  color: selectedItem != null ? Colors.black : Colors.grey,
                ),
              ],
            ),
          ),
          Icon(Icons.arrow_drop_down, size: context.d24, color: Colors.grey),
        ],
      ),
    );
  }

  void _onChangeSearch(String value) {
    ref.read(listPickerProvider(widget.data).notifier).changeKeyword(value);
  }

  void _showDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return DismissKeyboardWrapper(
          child: Consumer(
            builder: (context, ref, child) {
              final state = ref.watch(listPickerProvider(widget.data));
              return AlertDialog(
                contentPadding: EdgeInsets.zero,
                insetPadding: EdgeInsets.all(context.d32),
                backgroundColor: Colors.transparent,
                content: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: br16,
                  ),
                  height: context.screenHeight,
                  width: context.screenWidth,
                  child: Panel(
                    header: Container(
                      width: double.infinity,
                      padding: p16,
                      decoration: BoxDecoration(
                        border: Border(bottom: oneWidthCaskill),
                      ),
                      child: Row(
                        children: [
                          Expanded(child: Container()),
                          Expanded(
                            flex: 5,
                            child: CustomText(
                              text: widget.title,
                              fontWeight: FontWeight.w500,
                              textStyle: context.titleMedium,
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Expanded(
                            child: Align(
                              alignment: Alignment.centerRight,
                              child: CustomInkwellButton(
                                onTap: () {
                                  back();
                                  _controller.text = '';
                                  ref
                                      .read(
                                        listPickerProvider(
                                          widget.data,
                                        ).notifier,
                                      )
                                      .changeKeyword('');
                                },
                                child: Icon(
                                  Icons.close,
                                  size: context.d24,
                                  color: Colors.black54,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    body: Container(
                      width: double.infinity,
                      padding: p16,
                      child: Column(
                        children: [
                          CustomTextField(
                            controller: _controller,
                            placeHolder: 'Tìm kiếm theo từ khoá',
                            onChanged: _onChangeSearch,
                          ),
                          SizedBox(height: context.d16),
                          Expanded(
                            child: ListView(
                              children: state.displayLst
                                  .map(
                                    (e) => CustomInkwellButton(
                                      onTap: () {
                                        widget.onSelectItem(e);
                                      },
                                      child: Container(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: context.d8,
                                          vertical: context.d16,
                                        ),
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(
                                            context.d12,
                                          ),
                                          color: widget.value == e.value
                                              ? MyColors.primary
                                              : Colors.white,
                                          border: Border(
                                            bottom: oneWidthCaskill,
                                          ),
                                        ),
                                        child: widget.onRenderItem != null
                                            ? widget.onRenderItem!(e)
                                            : CustomText(
                                                text: e.key,
                                                color: widget.value == e.value
                                                    ? Colors.white
                                                    : Colors.black,
                                              ),
                                      ),
                                    ),
                                  )
                                  .toList(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }
}
