import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/theme/colors.dart';
import 'package:common_widgets/widgets/custom_text.dart';
import 'package:flutter/material.dart';

class CustomBadge extends StatelessWidget {
  const CustomBadge({
    super.key,
    required this.label,
  });

  final String label;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(context.d12),
        color: Colors.white,
      ),
      color: MyColors.primary,
      padding: EdgeInsets.symmetric(
        horizontal: context.d16,
        vertical: context.d8,
      ),
      child: CustomText(
        text: label,
        size: context.d14,
        fontWeight: FontWeight.w500,
      ),
    );
  }
}
