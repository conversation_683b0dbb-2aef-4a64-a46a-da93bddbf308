import 'package:common_widgets/constants/app_config_constant.dart';
import 'package:common_widgets/utils/location_util/location_util.dart';
import 'package:common_widgets/utils/manipulate_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:flutter_map_cancellable_tile_provider/flutter_map_cancellable_tile_provider.dart';
import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';

/// A customizable map view widget that displays the user's current location
/// and any additional markers.
///
/// This widget is designed to be clean, fast, and easy to use. It handles
/// location fetching, loading states, and error display internally.
class CustomMapView extends StatefulWidget {
  /// An optional list of additional markers to display on the map.
  final List<Marker> markers;

  /// An optional callback that is triggered when the map is ready and the
  /// user's location has been fetched. It provides the current position.
  final Function(LatLng position)? onMapReady;

  /// An optional [MapController] to programmatically control the map.
  final MapController? mapController;

  const CustomMapView({super.key, this.markers = const [], this.onMapReady, this.mapController});

  @override
  State<CustomMapView> createState() => _CustomMapViewState();
}

class _CustomMapViewState extends State<CustomMapView> {
  LatLng _currentPosition = const LatLng(0, 0);
  late final MapController _mapController;

  @override
  void initState() {
    super.initState();
    _mapController = widget.mapController ?? MapController();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _fetchCurrentLocation();
      }
    });
  }

  Future<void> _fetchCurrentLocation() async {
    await manipulate(context, () async {
      final position = await LocationUtil.instance.getCurrentLocationWithRetry(
        maxRetries: 2,
        timeout: const Duration(seconds: 15),
        accuracy: LocationAccuracy.high,
      );

      if (!mounted) return;

      final userPosition = LatLng(position.latitude, position.longitude);

      setState(() {
        _currentPosition = userPosition;
      });

      _mapController.move(userPosition, 16);

      widget.onMapReady?.call(userPosition);
    });
  }

  @override
  void dispose() {
    // Only dispose the controller if it was created internally
    if (widget.mapController == null) {
      _mapController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FlutterMap(
      mapController: _mapController,
      options: MapOptions(
        initialCenter: _currentPosition,
        initialZoom: 16,
        minZoom: 11,
        maxZoom: 18,
      ),
      children: [
        TileLayer(
          urlTemplate: AppConfig.mapTitleUrl,
          tileProvider: CancellableNetworkTileProvider(
            headers: {'referer': AppConfig.mapReferer},
          ),
        ),
        MarkerLayer(
          markers: [
            // Current Location Marker
            Marker(
              point: _currentPosition,
              width: 80,
              height: 80,
              child: const DefaultLocationMarker(),
            ),
            // Additional Markers
            ...widget.markers,
          ],
        ),
      ],
    );
  }
}

/// A default widget for representing the user's current location on the map.
class DefaultLocationMarker extends StatelessWidget {
  const DefaultLocationMarker({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Icon(Icons.circle, size: 28, color: Colors.blue.withAlpha(80)),
        const Icon(Icons.circle, size: 16, color: Colors.blue),
      ],
    );
  }
}
