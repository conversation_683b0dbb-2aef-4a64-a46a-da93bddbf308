import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/models/option_item.dart';
import 'package:common_widgets/theme/border.dart';
import 'package:common_widgets/theme/colors.dart';
import 'package:common_widgets/widgets/custom_inkwell_button.dart';
import 'package:common_widgets/widgets/custom_text.dart';
import 'package:common_widgets/widgets/panel.dart';
import 'package:flutter/material.dart';

class CustomDropdownPicker extends StatefulWidget {
  const CustomDropdownPicker(
      {super.key,
      required this.initItem,
      required this.items,
      required this.parentContext,
      required this.title,
      this.borderRadius,
      required this.onSelectedItem});

  final BuildContext parentContext;
  final List<OptionItem> items;
  final OptionItem initItem;
  final String title;
  final double? borderRadius;
  final Function(OptionItem) onSelectedItem;

  @override
  _CustomDropdownPickerState createState() => _CustomDropdownPickerState();
}

class _CustomDropdownPickerState extends State<CustomDropdownPicker> {
  OptionItem selectedValue = OptionItem(key: '', value: '');

  Future<void> _openDropdown(BuildContext context) async {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          elevation: 0,
          contentPadding: EdgeInsets.zero,
          backgroundColor: Colors.transparent,
          content: IntrinsicHeight(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(context.d16),
              ),
              child: Panel(
                header: Container(
                  width: double.maxFinite,
                  decoration:
                      BoxDecoration(border: Border(bottom: oneWidthCaskill)),
                  padding: EdgeInsets.all(context.d16),
                  child: CustomText(
                    text: widget.title,
                    size: context.d18,
                    fontWeight: FontWeight.w600,
                    textAlign: TextAlign.center,
                  ),
                ),
                body: Container(
                  width: double.maxFinite,
                  padding: EdgeInsets.all(context.d16),
                  child: Column(
                    children: widget.items.map((e) {
                      if (e.value == '-1') {
                        return Container();
                      }
                      return CustomInkwellButton(
                        onTap: () {
                          if (e.value == '-1') {
                            return;
                          }

                          setState(() {
                            selectedValue = e;
                          });

                          widget.onSelectedItem(e);

                          Navigator.of(context).pop();
                        },
                        child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(context.d16),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(context.d12),
                            color: selectedValue.value == e.value
                                ? Colors.red
                                : Colors.white,
                          ),
                          child: CustomText(
                            text: e.key,
                            size: context.d16,
                            color: selectedValue.value == e.value
                                ? Colors.white
                                : Colors.black,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  void initState() {
    super.initState();

    selectedValue = widget.initItem;
  }

  @override
  void didUpdateWidget(covariant CustomDropdownPicker oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (selectedValue != widget.initItem) {
      selectedValue = widget.initItem;
    }
  }

  @override
  Widget build(BuildContext context) {
    return CustomInkwellButton(
      onTap: () {
        _openDropdown(widget.parentContext);
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(context.d16),
          color: MyColors.catskillWhite,
          border: Border.all(
            color: Colors.grey.shade300,
            width: context.scaleWithValue(1),
          ),
        ),
        padding: EdgeInsets.all(context.d16),
        width: double.infinity,
        child: CustomText(
          text: selectedValue.key,
          size: context.d16,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }
}
