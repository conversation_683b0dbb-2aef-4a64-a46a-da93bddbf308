import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/extensions/text_style_extension.dart';
import 'package:common_widgets/theme/border.dart';
import 'package:common_widgets/widgets/custom_text.dart';
import 'package:flutter/material.dart';

class CustomGroupedList<T> extends StatelessWidget {
  const CustomGroupedList({
    super.key,
    required this.groups,
    this.groupHeaderBuilder,
    required this.groupItemBuilder,
    this.hasSpaceBetweenGroup = true,
    this.padding,
  });

  final Map<String, List<T>> groups;
  final Widget Function(String key)? groupHeaderBuilder;
  final Widget Function(T element, int index) groupItemBuilder;
  final bool hasSpaceBetweenGroup;
  final EdgeInsets? padding;

  @override
  Widget build(BuildContext context) {
    Widget buildGroupHeader(String key) {
      if (groupHeaderBuilder == null) {
        return Container(
          width: double.infinity,
          padding: EdgeInsets.all(context.d16),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              bottom: oneWidthCaskill,
            ),
          ),
          child: CustomText(
            text: key,
            textStyle: context.titleMedium,
          ),
        );
      }

      return groupHeaderBuilder!(key);
    }

    List<Widget> buildGroupItems(String key) {
      return groups[key]!
          .asMap()
          .entries
          .map((entry) => groupItemBuilder(entry.value, entry.key))
          .toList();
    }

    return ListView.builder(
      padding: padding,
      itemCount: groups.length,
      itemBuilder: (context, index) {
        final key = groups.keys.elementAt(index);
        final groupHeader = buildGroupHeader(key);
        final groupItems = buildGroupItems(key);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            groupHeader,
            ...groupItems,
            if (hasSpaceBetweenGroup) SizedBox(height: context.d16),
          ],
        );
      },
    );
  }
}
