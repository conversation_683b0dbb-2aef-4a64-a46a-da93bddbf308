// ignore_for_file: library_private_types_in_public_api

import 'package:common_widgets/extensions/debounce_extension.dart';
import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/extensions/text_style_extension.dart';
import 'package:common_widgets/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CustomTextField extends StatefulWidget {
  const CustomTextField({
    super.key,
    this.autocorrect,
    this.border,
    this.borderRadius,
    this.contentPadding,
    this.controller,
    this.enabledBorder,
    this.enableSuggestions,
    this.fillColor,
    this.focusBorder,
    this.textAlign = TextAlign.start,
    this.useSize = false,
    this.inputFormatters,
    this.isPassword,
    this.maxLines = 1,
    this.minLines,
    this.obscureText,
    this.onChanged,
    this.placeHolder,
    this.prefixIcon,
    this.readOnly = false,
    this.suffixIcon,
    this.type,
    this.value,
    this.label,
    this.labelStyle,
    this.labelText,
    this.suffix,
    this.size,
    this.maxLength,
  }) : assert(
         !useSize || (useSize && size != null),
         "<PERSON>ui lòng khai báo size nếu bạn bật useSize = true",
       );

  final bool readOnly;
  final bool useSize;
  final bool? autocorrect;
  final bool? enableSuggestions;
  final bool? isPassword;
  final bool? obscureText;
  final Color? fillColor;
  final String? labelText;
  final TextStyle? labelStyle;
  final Widget? label;
  final Widget? suffix;
  final double? borderRadius;
  final EdgeInsetsGeometry? contentPadding;
  final Function(String)? onChanged;
  final int? maxLines;
  final int? minLines;
  final List<TextInputFormatter>? inputFormatters;
  final OutlineInputBorder? border;
  final OutlineInputBorder? enabledBorder;
  final OutlineInputBorder? focusBorder;
  final TextAlign textAlign;
  final Size? size;
  final String? placeHolder;
  final String? value;
  final TextEditingController? controller;
  final TextInputType? type;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final int? maxLength;

  @override
  _CustomTextFieldState createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField> {
  bool _passwordVisible = true;
  final Debouncer _keywordDebouncer = Debouncer(
    delay: const Duration(milliseconds: 300),
  );
  late final TextEditingController controller;
  FocusNode? _focusNode;

  @override
  void initState() {
    super.initState();
    _passwordVisible = widget.obscureText ?? false;
    controller = TextEditingController(text: widget.value);
    _focusNode = FocusNode();
  }

  @override
  void dispose() {
    controller.dispose();
    _focusNode?.dispose();
    super.dispose();
  }

  void _toggle() {
    setState(() {
      _passwordVisible = !_passwordVisible;
    });
  }

  void onChange(String keyword) {
    _keywordDebouncer.call(() {
      if (mounted) {
        widget.onChanged?.call(keyword);
      }
    });
  }

  @override
  void didUpdateWidget(CustomTextField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.value != oldWidget.value && widget.value != null) {
      final currentSelection = controller.selection;
      controller.text = widget.value!;
      if (currentSelection.isValid) {
        controller.selection = currentSelection;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: widget.controller ?? controller,
      focusNode: _focusNode,
      readOnly: widget.readOnly,
      onChanged: onChange,
      maxLines: widget.maxLines,
      minLines: widget.minLines,
      inputFormatters: widget.inputFormatters,
      keyboardType: widget.type ?? TextInputType.text,
      obscureText: _passwordVisible,
      textAlign: widget.textAlign,
      style: context.bodyMedium,
      maxLength: widget.maxLength,
      decoration: InputDecoration(
        labelText: widget.labelText,
        labelStyle: widget.labelStyle,
        label: widget.label,
        contentPadding:
            widget.contentPadding ??
            EdgeInsets.symmetric(
              vertical: context.d16,
              horizontal: context.d16,
            ),
        isDense: true,
        hintText: widget.placeHolder ?? 'Tìm theo keyword',
        hintStyle: context.bodyMedium.copyWith(
          color: Colors.black38,
        ),
        prefixIcon: widget.prefixIcon,
        suffix: widget.suffix,
        suffixIcon: widget.isPassword == true
            ? IconButton(
                icon: Icon(
                  // Based on passwordVisible state choose the icon
                  _passwordVisible ? Icons.visibility : Icons.visibility_off,
                  color: Colors.black,
                ),
                onPressed: _toggle,
              )
            : widget.suffixIcon,
        filled: true,
        fillColor: widget.fillColor ?? Colors.white,
        enabledBorder:
            widget.enabledBorder ??
            OutlineInputBorder(
              borderRadius: BorderRadius.circular(
                widget.borderRadius ?? context.d12,
              ),
              borderSide: const BorderSide(width: 1, color: Colors.black12),
            ),
        focusedBorder:
            widget.focusBorder ??
            OutlineInputBorder(
              borderRadius: BorderRadius.circular(
                widget.borderRadius ?? context.d12,
              ),
              borderSide: BorderSide(width: 1, color: MyColors.primary),
            ),
        border:
            widget.border ??
            OutlineInputBorder(
              borderRadius: BorderRadius.circular(
                widget.borderRadius ?? context.d12,
              ),
            ),
      ),
    );
  }
}
