// ignore_for_file: library_private_types_in_public_api

import 'package:flutter/material.dart';

class CustomTabbar extends StatefulWidget {
  const CustomTabbar(
      {super.key,
      required this.initialIndex,
      required this.length,
      required this.title,
      required this.tabs,
      required this.tabView});

  final int initialIndex;
  final int length;
  final String title;

  ///Số lượng tab phải tương ứng với [lenght]
  final List<Widget> tabs;

  final TabBarView tabView;

  @override
  _TabbarState createState() => _TabbarState();
}

class _TabbarState extends State<CustomTabbar> {
  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      initialIndex: widget.initialIndex,
      length: widget.length,
      child: Scaffold(
        appBar: AppBar(
          title: Text(widget.title),
          bottom: TabBar(tabs: widget.tabs),
        ),
        body: widget.tabView,
      ),
    );
  }
}
