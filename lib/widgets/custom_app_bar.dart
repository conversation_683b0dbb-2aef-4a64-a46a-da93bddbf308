import 'package:common_widgets/extensions/text_style_extension.dart';
import 'package:common_widgets/theme/colors.dart';
import 'package:common_widgets/widgets/back_button.dart';
import 'package:common_widgets/widgets/custom_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final bool? automaticallyImplyLeading;
  final bool? centerTitle;
  final Border? border;
  final Color? backgroundColor;
  final double? titleSpacing;
  final Function()? onBack;
  final List<Widget>? actions;
  final String? title;
  final Widget? leading;
  final Widget? titleWidget;

  static final toolbarHeight = kToolbarHeight;

  const CustomAppBar({
    super.key,
    this.title,
    this.leading,
    this.actions,
    this.onBack,
    this.titleWidget,
    this.backgroundColor,
    this.centerTitle,
    this.automaticallyImplyLeading,
    this.titleSpacing,
    this.border,
  });

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      elevation: 0,
      scrolledUnderElevation: 0,
      titleSpacing: titleSpacing,
      backgroundColor: backgroundColor ?? MyColors.primary,
      leading: automaticallyImplyLeading == null || automaticallyImplyLeading == true
          ? leading ?? CustomBackButton(iconColor: Colors.white, onBack: onBack)
          : null,
      actions: actions,
      shape: border ?? const Border(bottom: BorderSide.none),
      toolbarHeight: toolbarHeight,
      systemOverlayStyle: SystemUiOverlayStyle.light,
      title:
          titleWidget ??
          CustomText(
            text: title ?? '',
            fontWeight: FontWeight.w600,
            color: Colors.white,
            textStyle: context.titleMedium,
          ),
      centerTitle: centerTitle ?? true,
      automaticallyImplyLeading: automaticallyImplyLeading ?? true,
    );
  }
}
