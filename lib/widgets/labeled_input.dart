import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/extensions/text_style_extension.dart';
import 'package:common_widgets/widgets/custom_text.dart';
import 'package:flutter/material.dart';

class LabeledInput extends StatelessWidget {
  final EdgeInsets? padding;
  final String label;
  final TextStyle? style;
  final Widget input;
  final Widget? labelAppend;
  final double? spacingBtwLabelInput;
  final bool required;

  const LabeledInput({
    super.key,
    required this.label,
    required this.input,
    this.labelAppend,
    this.padding,
    this.required = false,
    this.spacingBtwLabelInput,
    this.style,
  }) : assert(required == false || labelAppend == null, 'Nếu required = true, thì không được dùng labelAppend');

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          CustomText(
            text: label,
            textStyle: style ?? context.labelLarge,
            append: required == true
                ? CustomText(
                    text: ' (*)',
                    color: Colors.red,
                    textStyle: style ?? context.labelLarge,
                  )
                : labelAppend,
          ),
          SizedBox(height: spacingBtwLabelInput ?? context.d16),
          input,
        ],
      ),
    );
  }
}
