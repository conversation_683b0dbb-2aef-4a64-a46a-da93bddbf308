import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:common_widgets/theme/colors.dart';
import 'package:flutter/material.dart';

class CustomSwitch extends StatefulWidget {
  const CustomSwitch({super.key, required this.onChange, this.label, this.size = SwitchSize.medium});

  final void Function(bool) onChange;
  final Widget? label;
  final SwitchSize size;

  @override
  State<CustomSwitch> createState() => _CustomSwitchState();
}

class _CustomSwitchState extends State<CustomSwitch> {
  bool checked = false;

  void _onChange(bool value) {
    setState(() {
      checked = value;
    });

    widget.onChange(value);
  }

  Color resolveState(Set<WidgetState> states) {
    if (states.firstOrNull != WidgetState.selected) {
      return Colors.black26;
    }
    return MyColors.primary;
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Transform.scale(
          scale: widget.size.scale,
          child: SizedBox(
            width: 50,
            height: 30,
            child: Switch(
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              value: checked,
              onChanged: _onChange,
              trackColor: const WidgetStatePropertyAll(Colors.white),
              trackOutlineColor: WidgetStateColor.resolveWith(resolveState),
              thumbColor: WidgetStateColor.resolveWith(resolveState),
            ),
          ),
        ),
        if (widget.label != null)
          Padding(
            padding: EdgeInsets.only(left: context.d8),
            child: widget.label!,
          ),
      ],
    );
  }
}

enum SwitchSize {
  small(scale: 0.5),
  medium(scale: 0.75),
  large(scale: 1.0);

  final double scale;

  const SwitchSize({required this.scale});
}
