// ignore_for_file: library_private_types_in_public_api

import 'package:flutter/material.dart';

class CustomInkwellButton extends StatefulWidget {
  const CustomInkwellButton(
      {super.key,
      required this.child,
      this.onTap,
      this.animatedContainerColor,
      this.onLongPress,
      this.splashColor,
      this.highlightColor,
      this.borderRadius,
      this.radius,
      this.customBorder});

  final Widget child;
  final Function()? onTap;
  final Color? animatedContainerColor;
  final GestureLongPressCallback? onLongPress;
  final Color? splashColor;
  final Color? highlightColor;
  final BorderRadius? borderRadius;
  final double? radius;
  final ShapeBorder? customBorder;

  @override
  _CustomInkwellButtonState createState() => _CustomInkwellButtonState();
}

class _CustomInkwellButtonState extends State<CustomInkwellButton> with SingleTickerProviderStateMixin {
  bool isTapped = false;

  @override
  void initState() {
    super.initState();
  }

  void onTap() {
    setState(() {
      isTapped = !isTapped;
    });

    if (widget.onTap == null) {
      return;
    }

    widget.onTap!();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      onLongPress: widget.onLongPress,
      splashColor: widget.splashColor,
      highlightColor: widget.highlightColor,
      borderRadius: widget.borderRadius,
      radius: widget.radius,
      customBorder: widget.customBorder,
      child: AnimatedOpacity(
        opacity: isTapped ? 0.65 : 1.0,
        onEnd: () {
          setState(() {
            isTapped = false;
          });
        },
        duration: const Duration(milliseconds: 100),
        child: widget.child,
      ),
    );
  }
}
