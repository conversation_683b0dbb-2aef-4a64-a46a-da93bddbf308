import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:flutter/material.dart';

class CustomIconButton extends StatelessWidget {
  const CustomIconButton({
    super.key,
    required this.onPress,
    required this.icon,
    this.onLongPress,
  });

  final VoidCallback onPress;
  final IconData icon;
  final VoidCallback? onLongPress;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onLongPress: onLongPress,
      child: IconButton(
        onPressed: onPress,
        icon: Icon(
          icon,
          size: context.d32,
          color: Colors.white,
        ),
      ),
    );
  }
}
