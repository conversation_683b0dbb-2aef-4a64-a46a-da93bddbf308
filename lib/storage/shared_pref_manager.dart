import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

final sharedPrefsProvider = Provider<SharedPrefManager>((ref) {
  return SharedPrefManager();
});

class SharedPrefManager {
  SharedPreferences? _prefs;
  static const String _codeStaffKey = 'codestaff';

  // Completer to track initialization
  bool _isInitialized = false;

  SharedPrefManager();

  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      _prefs = await SharedPreferences.getInstance();
      _isInitialized = true;
    }
  }

  Future<void> setString(String key, String value) async {
    await _ensureInitialized();
    await _prefs!.setString(key, value);
  }

  Future<String?> getString(String key) async {
    await _ensureInitialized();
    return _prefs!.getString(key);
  }

  Future<void> setBool(String key, bool value) async {
    await _ensureInitialized();
    await _prefs!.setBool(key, value);
  }

  Future<bool?> getBool(String key) async {
    await _ensureInitialized();
    return _prefs!.getBool(key);
  }

  Future<void> setInt(String key, int value) async {
    await _ensureInitialized();
    await _prefs!.setInt(key, value);
  }

  Future<int?> getInt(String key) async {
    await _ensureInitialized();
    return _prefs!.getInt(key);
  }

  Future<void> setDouble(String key, double value) async {
    await _ensureInitialized();
    await _prefs!.setDouble(key, value);
  }

  Future<double?> getDouble(String key) async {
    await _ensureInitialized();
    return _prefs!.getDouble(key);
  }

  Future<void> setList(String key, List<String> value) async {
    await _ensureInitialized();
    await _prefs!.setStringList(key, value);
  }

  Future<List<String>?> getList(String key) async {
    await _ensureInitialized();
    return _prefs!.getStringList(key);
  }

  Future<void> remove(String key) async {
    await _ensureInitialized();
    await _prefs!.remove(key);
  }

  Future<void> clear() async {
    await _ensureInitialized();
    await _prefs!.clear();
  }

  Future<void> setObject(String key, Object value) async {
    await _ensureInitialized();
    await _prefs!.setString(key, jsonEncode(value));
  }

  Future<T?> getObject<T>(String key) async {
    await _ensureInitialized();
    final value = _prefs!.getString(key);
    return value != null ? jsonDecode(value) as T : null;
  }

  Future<void> setCodeStaff(String value) async {
    await setString(_codeStaffKey, value);
  }

  Future<String?> getCodeStaff() async {
    await _ensureInitialized();
    return _prefs!.getString(_codeStaffKey);
  }
}
