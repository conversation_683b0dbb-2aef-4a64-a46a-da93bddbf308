class ServerException implements Exception {
  final int? statusCode;
  final String message;

  ServerException(this.statusCode, this.message);
}

class NetworkException implements Exception {
  final String message = 'No internet connection';
}

class TimeoutException implements Exception {
  final String message = 'Request timed out';
}

class RequestCancelledException implements Exception {
  final String message = 'Request was cancelled';
}

class CacheException implements Exception {
  final String message = 'Cache error occurred';
}

class ValidationException implements Exception {
  final String message;

  ValidationException(this.message);
} 