import 'package:flutter/material.dart';

enum ErrorType {
  network,
  auth,
  validation,
  server,
  unknown,
  notFound,
}

class AppError {
  final String message;
  final ErrorType type;
  final String? code;
  final dynamic originalError;
  final StackTrace? stackTrace;

  const AppError({
    required this.message,
    required this.type,
    this.code,
    this.originalError,
    this.stackTrace,
  });

  factory AppError.network(String message, {String? code, dynamic originalError, StackTrace? stackTrace}) {
    return AppError(
      message: message,
      type: ErrorType.network,
      code: code,
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  factory AppError.auth(String message, {String? code, dynamic originalError, StackTrace? stackTrace}) {
    return AppError(
      message: message,
      type: ErrorType.auth,
      code: code,
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  factory AppError.validation(String message, {String? code, dynamic originalError, StackTrace? stackTrace}) {
    return AppError(
      message: message,
      type: ErrorType.validation,
      code: code,
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  factory AppError.server(String message, {String? code, dynamic originalError, StackTrace? stackTrace}) {
    return AppError(
      message: message,
      type: ErrorType.server,
      code: code,
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  factory AppError.notFound(String message, {String? code, dynamic originalError, StackTrace? stackTrace}) {
    return AppError(
      message: message,
      type: ErrorType.notFound,
      code: code,
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  factory AppError.unknown(String message, {String? code, dynamic originalError, StackTrace? stackTrace}) {
    return AppError(
      message: message,
      type: ErrorType.unknown,
      code: code,
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  String get title {
    switch (type) {
      case ErrorType.network:
        return 'Network Error';
      case ErrorType.auth:
        return 'Authentication Error';
      case ErrorType.validation:
        return 'Validation Error';
      case ErrorType.server:
        return 'Server Error';
      case ErrorType.notFound:
        return 'Not Found';
      case ErrorType.unknown:
        return 'Unknown Error';
    }
  }

  IconData get icon {
    switch (type) {
      case ErrorType.network:
        return Icons.wifi_off;
      case ErrorType.auth:
        return Icons.lock;
      case ErrorType.validation:
        return Icons.error_outline;
      case ErrorType.server:
        return Icons.cloud_off;
      case ErrorType.notFound:
        return Icons.search_off;
      case ErrorType.unknown:
        return Icons.error_outline;
    }
  }
} 