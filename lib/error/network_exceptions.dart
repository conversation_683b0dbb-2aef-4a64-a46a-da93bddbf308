/// Base class for API-related exceptions.
class ApiException implements Exception {
  final String message;
  final int? statusCode;

  ApiException(this.message, {this.statusCode});

  @override
  String toString() {
    if (statusCode != null) {
      return 'ApiException [Status Code: $statusCode]: $message';
    }
    return 'ApiException: $message';
  }
}

/// Specific network-related exceptions.
class NetworkException implements Exception {
  final String message;

  NetworkException(this.message);

  @override
  String toString() => message;
}

class BadRequestException extends NetworkException {
  BadRequestException([super.message = 'Invalid request.']);
}

class UnauthorisedException extends NetworkException {
  UnauthorisedException([super.message = 'Access denied.']);
}

class ForbiddenException extends NetworkException {
  ForbiddenException([super.message = 'Forbidden.']);
}

class NotFoundException extends NetworkException {
  NotFoundException([super.message = 'Resource not found.']);
}

class ConflictException extends NetworkException {
  ConflictException([super.message = 'Conflict occurred.']);
}

class InternalServerErrorException extends NetworkException {
  InternalServerErrorException([super.message = 'Internal server error.']);
}

class UnprocessableEntityException extends NetworkException {
  UnprocessableEntityException([super.message = 'Unprocessable Entity.']);
}

class DeadlineExceededException extends NetworkException {
  DeadlineExceededException([super.message = 'Request timeout.']);
}

class CancelException extends NetworkException {
  CancelException([super.message = 'Request cancelled.']);
}

class BadCertificateException extends NetworkException {
  BadCertificateException([super.message = 'Bad certificate.']);
}

class ConnectionErrorException extends NetworkException {
  ConnectionErrorException([super.message = 'Connection error.']);
}

class UnknownException extends NetworkException {
  UnknownException([super.message = 'An unknown network error occurred.']);
}
