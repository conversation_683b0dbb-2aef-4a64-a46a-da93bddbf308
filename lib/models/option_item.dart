import 'package:common_widgets/extensions/string_extension.dart';

class OptionItem<T> {
  final String key;
  final T value;
  final dynamic extraData;
  final dynamic extraData2;

  String get unsignedKey {
    return key.toSlug(separator: '');
  }

  OptionItem({required this.key, required this.value, this.extraData, this.extraData2});

  factory OptionItem.blank() {
    return OptionItem(key: '', value: '' as T);
  }

  factory OptionItem.fromJson(Map<String, dynamic> json) {
    return OptionItem(
      key: json['key'] as String,
      value: json['value'] as T,
      extraData: json['extraData'],
      extraData2: json['extraData2'],
    );
  }

  factory OptionItem.fromApi(Map<String, dynamic> json, Map<String, String> keyMapping) {
    return OptionItem(
      key: json[keyMapping['key']] as String,
      value: json[keyMapping['value']] as T,
      extraData: json[keyMapping['extraData']],
      extraData2: json[keyMapping['extraData2']],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'value': value,
      'extraData': extraData,
      'extraData2': extraData2,
    };
  }

  OptionItem<T> copyWith({
    String? key,
    T? value,
    dynamic extraData,
    dynamic extraData2,
  }) {
    return OptionItem<T>(
      key: key ?? this.key,
      value: value ?? this.value,
      extraData: extraData ?? this.extraData,
      extraData2: extraData2 ?? this.extraData2,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'key': key,
      'value': value,
      'extraData': extraData,
      'extraData2': extraData2,
    };
  }

  factory OptionItem.fromMap(Map<String, dynamic> map) {
    return OptionItem<T>(
      key: map['key'] ?? '',
      value: map['value'] ?? '',
      extraData: map['extraData'],
      extraData2: map['extraData2'],
    );
  }
}
