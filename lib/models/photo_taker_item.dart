import 'dart:convert';

class PhotoTakerItem {
  final String path;
  final bool hasDone;
  final bool isLocal;

  PhotoTakerItem({required this.path, required this.hasDone, this.isLocal = false});

  Map<String, dynamic> toMap() {
    return {
      'path': path,
      'hasDone': hasDone,
      'isLocal': isLocal,
    };
  }

  factory PhotoTakerItem.fromMap(Map<String, dynamic> map) {
    return PhotoTakerItem(
      path: map['path'] ?? '',
      hasDone: map['hasDone'],
      isLocal: map['isLocal'],
    );
  }

  String toJson() => json.encode(toMap());

  factory PhotoTakerItem.fromJson(String source) => PhotoTakerItem.fromMap(json.decode(source));

  PhotoTakerItem copyWith({
    String? path,
    bool? hasDone,
    bool? isLocal,
  }) {
    return PhotoTakerItem(
      path: path ?? this.path,
      hasDone: hasDone ?? this.hasDone,
      isLocal: isLocal ?? this.isLocal,
    );
  }
}
