enum WorkingPlanType {
  inPlan('<PERSON><PERSON> Tuyen'),
  outPlan('Ngoai Tuyen'),
  all('All');

  const WorkingPlanType(this.value);
  final String value;

  // Useful methods
  static WorkingPlanType fromString(String value) {
    return WorkingPlanType.values.firstWhere(
      (status) => status.value == value,
      orElse: () => throw ArgumentError('Unknown plan status: $value'),
    );
  }

  @override
  String toString() => value;
}
