import 'dart:io';

import 'package:common_widgets/constants/app_config_constant.dart';
import 'package:common_widgets/error/network_exceptions.dart';
import 'package:common_widgets/storage/shared_pref_manager.dart';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final dioClientProvider = Provider<DioClient>((ref) {
  final sharedPrefs = ref.read(sharedPrefsProvider);

  final dio = Dio()
    ..options.baseUrl = AppConfig.baseUrl
    ..options.connectTimeout = const Duration(seconds: 30)
    ..options.receiveTimeout = const Duration(seconds: 30)
    ..options.headers = {'Content-Type': 'application/json', 'Authorization': AppConfig.token}
    ..interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          final codeStaff = await sharedPrefs.getCodeStaff();
          options.headers['_userLogin'] = codeStaff;
          return handler.next(options);
        },
      ),
    );

  // Configure Dio to accept all certificates
  (dio.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () {
    final client = HttpClient();
    client.badCertificateCallback = (X509Certificate cert, String host, int port) => true;
    return client;
  };

  return DioClient(dio);
});

class DioClient {
  final Dio _dio;

  DioClient(this._dio);

  // --- Generic methods for fetching and mapping data --- //

  Future<Response> delete(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      _processResponse(response);
      return response;
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Convenience DELETE method that automatically unwraps Result<T> and returns T directly
  /// Throws exception if API call fails
  Future<T> deleteData<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    required T Function(dynamic) parser,
  }) async {
    final result = await deleteJson<Result<T>>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      parser: (json) => Result.fromApi(json),
    );

    if (!result.isSuccess()) {
      throw ApiException(result.getMessage());
    }

    return parser(result.getData());
  }

  /// Performs a DELETE request and maps the response data using a parser.
  Future<T> deleteJson<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    required T Function(dynamic) parser,
  }) async {
    try {
      final response = await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      final processedData = _processResponse(response);
      return parser(processedData);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  // --- Original methods returning raw Response (can still be used) --- //

  Future<Response> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      _processResponse(response); // Still process for status checks
      return response;
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Convenience GET method that automatically unwraps Result<T> and returns T directly
  /// Throws exception if API call fails
  Future<T> getData<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    required T Function(dynamic) parser,
  }) async {
    final result = await getJson<Result<dynamic>>(
      path,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      parser: (json) {
        return Result<dynamic>.fromApi(json as Map<String, dynamic>);
      },
    );

    if (!result.isSuccess()) {
      throw ApiException(result.getMessage());
    }

    return parser(result.getData());
  }

  /// Safe GET method that returns null on error instead of throwing
  Future<T?> getDataSafe<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    required T Function(dynamic) parser,
    T? defaultValue,
  }) async {
    try {
      return await getData<T>(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        parser: parser,
      );
    } catch (e) {
      return defaultValue;
    }
  }

  /// Performs a GET request and maps the response data using a parser.
  Future<T> getJson<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    required T Function(dynamic) parser, // Function to map response.data to T
  }) async {
    try {
      final response = await _dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      final processedData = _processResponse(response);
      return parser(processedData);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  Future<Response> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      _processResponse(response);
      return response;
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  // --- Convenience methods for auto-unwrapping Result<T> --- //

  /// Convenience POST method that automatically unwraps Result<T> and returns T directly
  /// Throws exception if API call fails
  Future<T> postData<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    required T Function(dynamic) parser,
  }) async {
    final result = await postJson<Result<T>>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      parser: (json) => Result.fromApi(json),
    );

    if (!result.isSuccess()) {
      throw ApiException(result.getMessage());
    }

    return parser(result.getData());
  }

  /// Safe POST method that returns null on error instead of throwing
  Future<T?> postDataSafe<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    required T Function(dynamic) parser,
    T? defaultValue,
  }) async {
    try {
      return await postData<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        parser: parser,
      );
    } catch (e) {
      return defaultValue;
    }
  }

  /// Performs a POST request and maps the response data using a parser.
  Future<T> postJson<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    required T Function(dynamic) parser,
  }) async {
    try {
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      final processedData = _processResponse(response);
      return parser(processedData);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  Future<Response> put(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      _processResponse(response);
      return response;
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Convenience PUT method that automatically unwraps Result<T> and returns T directly
  /// Throws exception if API call fails
  Future<T> putData<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    required T Function(dynamic) parser,
  }) async {
    final result = await putJson<Result<T>>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
      parser: (json) => Result.fromApi(json),
    );

    if (!result.isSuccess()) {
      throw ApiException(result.getMessage());
    }

    return parser(result.getData());
  }

  /// Performs a PUT request and maps the response data using a parser.
  Future<T> putJson<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    required T Function(dynamic) parser,
  }) async {
    try {
      final response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      final processedData = _processResponse(response);
      return parser(processedData);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  dynamic _handleError(DioException error) {
    // Centralized DioError handling and mapping to NetworkExceptions
    if (error.response != null) {
      final statusCode = error.response!.statusCode;
      final dynamic responseData = error.response!.data;
      final String errorMessage = responseData?.toString() ?? 'Unknown API Error';

      switch (statusCode) {
        case 400:
          throw BadRequestException(errorMessage);
        case 401:
          throw UnauthorisedException(errorMessage);
        case 403:
          throw ForbiddenException(errorMessage);
        case 404:
          throw NotFoundException(errorMessage);
        case 409:
          throw ConflictException(errorMessage);
        case 422:
          throw UnprocessableEntityException(errorMessage);
        case 500:
          throw InternalServerErrorException(errorMessage);
        default:
          throw ApiException(error.message ?? errorMessage, statusCode: statusCode);
      }
    } else {
      // Handle Dio errors without a response (network issues, timeouts, etc.)
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          throw DeadlineExceededException(error.message ?? 'Timeout Error');
        case DioExceptionType.cancel:
          throw CancelException(error.message ?? 'Request cancelled');
        case DioExceptionType.badCertificate:
          throw BadCertificateException(error.message ?? 'Bad certificate');
        case DioExceptionType.connectionError:
          throw ConnectionErrorException(error.message ?? 'Connection error');
        case DioExceptionType.badResponse:
          throw ApiException(error.message ?? 'Bad response received');
        case DioExceptionType.unknown:
          throw UnknownException(error.message ?? 'An unknown error occurred');
      }
    }
  }

  // --- Private helper methods --- //

  dynamic _processResponse(Response response) {
    // Centralized response processing (e.g., status code checks)
    if (response.statusCode != null && response.statusCode! >= 200 && response.statusCode! < 300) {
      return response.data; // Return raw data if successful
    } else {
      // You can add more sophisticated error handling here based on status code
      // and potentially the structure of response.data for error messages.
      // For now, rethrowing the original DioException from _handleError is common.
      // If the _handleError already throws, this part might be redundant,
      // but it serves as a clear place for post-request, pre-mapping checks.
      // Example: throw ApiException('API returned status code ${response.statusCode}', statusCode: response.statusCode);
      // Re-throwing the DioException from _handleError is usually sufficient
      // as it would have captured the bad response status.
      throw Exception(
        'API returned status code ${response.statusCode}',
      ); // Fallback if _handleError didn't catch it
    }
  }
}

class Result<T> {
  final T data;
  final String message;
  final String status;
  final int code;

  Result({required this.data, required this.message, required this.status, required this.code});

  factory Result.fromApi(Map<String, dynamic> json) {
    return Result(
      data: json['Data'] as T,
      message: json['Message'] ?? '',
      status: json['Status'] ?? '',
      code: json['Code'] ?? 200,
    );
  }
}

extension ResultExtension on Result {
  T getData<T>() => data as T;

  String getMessage() => message;

  bool isSuccess() => code == 200 && status == 'Success';
}
