import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';

extension ConsumerStateRefExtension<T extends ConsumerStatefulWidget> on ConsumerState<T> {
  /// Safely executes a callback after the first frame has been rendered.
  /// This is useful for `ref.read` calls in `initState` that modify state
  /// to prevent 'setState() called during build' errors.
  Future<void> safeInitState(FutureOr<void> Function() callback) async {
    await Future.microtask(() async {
      if (mounted) {
        await callback();
      }
    });
  }
}
