import 'package:camera/camera.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

extension XFileExtension on XFile {
  Future<MultipartFile> toMultipartFile() async {
    if (kIsWeb || path.isEmpty == true) {
      final bytes = await readAsBytes();

      return MultipartFile.fromBytes(
        bytes,
        filename: 'coke_verification',
      );
    } else {
      return await MultipartFile.fromFile(
        path,
        filename: 'coke_verification',
      );
    }
  }
}
