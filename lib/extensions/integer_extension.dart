import 'package:intl/intl.dart';

extension IntegerExtension on int {
  String toVietnameseCurrency() {
    final formatter = NumberFormat.currency(locale: 'vi', symbol: '₫');
    return formatter.format(this);
  }

  ///Tạo ra một danh sách năm gồm: 5 năm tr<PERSON>, năm hiện tại, 5 năm sau
  List<int> generateListYear({int before = 1, int after = 2}) {
    List<int> years = [];

    for (var i = this - before, j = this + after; i <= j; i++) {
      years.add(i);
    }

    return years;
  }

  double percent(int total) {
    double percentage = (this / total) * 100;
    return double.parse(percentage.toStringAsFixed(2));
  }
}
