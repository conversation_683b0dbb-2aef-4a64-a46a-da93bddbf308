import 'dart:convert';

import 'package:common_widgets/widgets/alert.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

extension StringExtension on String {
  static const String from =
      'àáãảạăằắẳẵặâầấẩẫậèéẻẽẹêềếểễệđùúủũụưừứửữựòóỏõọôồốổỗộơờớởỡợìíỉĩịäëïîöüûñçýỳỹỵỷ';
  static const String to =
      'aaaaaaaaaaaaaaaaaeeeeeeeeeeeduuuuuuuuuuuoooooooooooooooooiiiiiaeiiouuncyyyyy';

  List<T> parseToList<T>(T Function(Map<String, dynamic>) fromMap) {
    List<T> lst = [];

    try {
      final List<dynamic> lstDy = jsonDecode(this);

      lst = lstDy.map((e) => fromMap(Map<String, dynamic>.from(e))).toList();
    } catch (e) {
      Alert.danger(message: 'Có lỗi xảy ra - ${e.toString()}');
    }

    return lst;
  }

  String replaceByRegex({required RegExp regex, String replaceTo = ''}) {
    final newString = replaceAllMapped(regex, (match) {
      return replaceTo;
    });

    return newString;
  }

  Color toColor() {
    //* Remove any leading # character
    final String hexColor = replaceAll("#", "");

    //* Convert the hexadecimal color code to an integer
    int hexValue = int.parse(hexColor, radix: 16);

    //* Create a Color object from the integer value
    return Color(hexValue | 0xFF000000);
  }

  DateTime toDateTime() {
    try {
      return DateTime.parse(this);
    } catch (e) {
      // Alert.danger(message: e.toString());
      return DateTime(1990, 1, 1);
    }
  }

  DateTime toDateTimeFromFormat({String format = 'dd/MM/yyyy HH:mm:ss'}) {
    var inputFormat = DateFormat(format);
    return inputFormat.parse(this);
  }

  double toDouble() {
    try {
      return double.parse(this);
    } catch (e) {
      return 0;
    }
  }

  int toInt() {
    try {
      return int.parse(this);
    } catch (e) {
      return 0;
    }
  }

  String toSlug({String separator = '-'}) {
    if (isEmpty == true) {
      return '';
    }

    var str = toLowerCase();

    for (var i = 0; i < from.length; i++) {
      str = str.replaceAll(from[i], to[i]);
    }

    str = str.toLowerCase().trim();

    str = str.replaceAll(RegExp(r'[^a-z0-9\-]'), separator);

    str = str.replaceAll(RegExp(r'-+'), separator);

    return str;
  }
}

extension StringNullableExtension on String? {
  bool get isNullOrEmpty => this == null || this!.isEmpty;
}
