import 'package:common_widgets/extensions/text_size_extension.dart';
import 'package:flutter/material.dart';

extension TextStyleExtension on BuildContext {
  static const String _fontFamily = 'SFPro';

  TextStyle get titleLargeThan => TextStyle(
    fontFamily: _fontFamily,
    fontSize: d20,
    fontWeight: FontWeight.w700,
  );

  TextStyle get titleLarge => TextStyle(
    fontFamily: _fontFamily,
    fontSize: d18,
    fontWeight: FontWeight.w600,
  );

  TextStyle get titleMedium => TextStyle(
    fontFamily: _fontFamily,
    fontSize: d16, // Corrected from 18. A bold 16 is a standard medium title.
    fontWeight: FontWeight.w600, // Heavier weight for emphasis
  );

  TextStyle get titleSmall => TextStyle(
    fontFamily: _fontFamily,
    fontSize: d14, // Corrected from 16
    fontWeight: FontWeight.w600,
  );

  TextStyle get bodyLarge => TextStyle(
    fontFamily: _fontFamily,
    fontSize: d18,
    fontWeight: FontWeight.w400, // Changed from w500 for better readability
  );

  TextStyle get bodyMedium => TextStyle(
    fontFamily: _fontFamily,
    fontSize: d16,
    fontWeight:
        FontWeight.w400, // Weight remains w400 for consistency with bodyLarge
  );

  TextStyle get bodySmall => TextStyle(
    fontFamily: _fontFamily,
    fontSize: d14,
    fontWeight: FontWeight.w400,
  );

  TextStyle get labelLarge => TextStyle(
    fontFamily: _fontFamily,
    fontSize: d16,
    fontWeight: FontWeight.w600, // Changed from w600 for better balance
  );

  TextStyle get labelMedium => TextStyle(
    fontFamily: _fontFamily,
    fontSize: d14,
    fontWeight: FontWeight.w600, // Changed from w600
  );

  TextStyle get labelSmall => TextStyle(
    fontFamily: _fontFamily,
    fontSize: d12, // Using scaleWithValue for custom size
    fontWeight: FontWeight.w600,
  );
}
