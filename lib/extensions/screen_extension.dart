import 'package:flutter/material.dart';

extension ScreenExtension on BuildContext {
  double get screenWidth => MediaQuery.of(this).size.width;
  double get screenHeight => MediaQuery.of(this).size.height;
  double get statusBarHeight => MediaQuery.of(this).padding.top;
  double get bottomBarHeight => MediaQuery.of(this).padding.bottom;

  double scaleFontSize(double fontSize) {
    return MediaQuery.of(this).textScaler.scale(fontSize);
  }
}

final br12 = BorderRadius.circular(12);
final br16 = BorderRadius.circular(16);
final br24 = BorderRadius.circular(24);
final br32 = BorderRadius.circular(32);
final br40 = BorderRadius.circular(40);
final br48 = BorderRadius.circular(48);
final br56 = BorderRadius.circular(56);
final br64 = BorderRadius.circular(64);
final br72 = BorderRadius.circular(72);

final p4 = EdgeInsets.all(4);
final p8 = EdgeInsets.all(8);
final p12 = EdgeInsets.all(12);
final p16 = EdgeInsets.all(16);
final p24 = EdgeInsets.all(24);
final p32 = EdgeInsets.all(32);
final p40 = EdgeInsets.all(40);
final p48 = EdgeInsets.all(48);
final p56 = EdgeInsets.all(56);

final pb4 = EdgeInsets.only(bottom: 4);
final pb8 = EdgeInsets.only(bottom: 8);
final pb12 = EdgeInsets.only(bottom: 12);
final pb16 = EdgeInsets.only(bottom: 16);
final pb24 = EdgeInsets.only(bottom: 24);
final pb32 = EdgeInsets.only(bottom: 32);
final pb40 = EdgeInsets.only(bottom: 40);
final pb48 = EdgeInsets.only(bottom: 48);
final pb56 = EdgeInsets.only(bottom: 56);

final pl4 = EdgeInsets.only(left: 4);
final pl8 = EdgeInsets.only(left: 8);
final pl12 = EdgeInsets.only(left: 12);
final pl16 = EdgeInsets.only(left: 16);
final pl24 = EdgeInsets.only(left: 24);
final pl32 = EdgeInsets.only(left: 32);
final pl40 = EdgeInsets.only(left: 40);
final pl48 = EdgeInsets.only(left: 48);
final pl56 = EdgeInsets.only(left: 56);

final pr4 = EdgeInsets.only(right: 4);
final pr8 = EdgeInsets.only(right: 8);
final pr12 = EdgeInsets.only(right: 12);
final pr16 = EdgeInsets.only(right: 16);
final pr24 = EdgeInsets.only(right: 24);
final pr32 = EdgeInsets.only(right: 32);
final pr40 = EdgeInsets.only(right: 40);
final pr48 = EdgeInsets.only(right: 48);
final pr56 = EdgeInsets.only(right: 56);

final pt4 = EdgeInsets.only(top: 4);
final pt8 = EdgeInsets.only(top: 8);
final pt12 = EdgeInsets.only(top: 12);
final pt16 = EdgeInsets.only(top: 16);
final pt24 = EdgeInsets.only(top: 24);
final pt32 = EdgeInsets.only(top: 32);
final pt40 = EdgeInsets.only(top: 40);
final pt48 = EdgeInsets.only(top: 48);
final pt56 = EdgeInsets.only(top: 56);
