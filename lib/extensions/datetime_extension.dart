import 'package:intl/intl.dart';

extension DateTimeExtension on DateTime {
  ///Convert datetime sang định dạng theo **[format]**, nếu bỏ trống, format mặc định là dd/MM/yyyy
  String toLocalFormat({String? format = 'dd/MM/yyyy'}) {
    final formatter = DateFormat(format);
    return formatter.format(this);
  }

  ///Convert datetime sang định dạng **dd/MM/yyyy HH:mm:ss**
  String toDateTimeLocalFormat() {
    return toLocalFormat(format: 'dd/MM/yyyy HH:mm:ss');
  }

  DateTime resetToMidnight() {
    return DateTime(year, month, day);
  }

  ///Kiểm tra ngày có nằm giữa 2 ngày chỉ định không
  bool isBetween({required DateTime fromDate, required DateTime toDate}) {
    if (isBefore(fromDate) || isAfter(toDate)) {
      return false;
    }

    return true;
  }

  int toTimestamp() {
    return millisecondsSinceEpoch;
  }

  DateTime mergeTimeFrom(DateTime another){
    return DateTime(year, month, day, another.hour, another.minute, another.second);
  }
}
