import 'package:common_widgets/extensions/screen_extension.dart';
import 'package:flutter/widgets.dart';

extension TextSizeExtension on BuildContext {
  double get d8 => 8;
  double get d12 => 12;
  double get d14 => 14;
  double get d16 => 16;
  double get d18 => 18;
  double get d20 => 20;
  double get d24 => 24;
  double get d28 => 28;
  double get d32 => 32;
  double get d48 => 48;
  double get d56 => 56;
  double get d64 => 64;
  double get d128 => 128;

  double scaleWithValue(double value) => scaleFontSize(value);
}

//move all d8, d12 above to below
