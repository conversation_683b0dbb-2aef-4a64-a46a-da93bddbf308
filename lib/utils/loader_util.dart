import 'package:common_widgets/widgets/loader.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class LoaderUtils {
  static WidgetRef? _ref;

  // Initialize the ref (call this once in your app)
  static void initialize(WidgetRef ref) {
    _ref = ref;
  }

  // Show loader with optional message
  static void show({String? message}) {
    if (_ref != null) {
      _ref!
          .read(loaderProvider.notifier)
          .toggleLoader(canShow: true, message: message ?? 'Đang xử lý...');
    }
  }

  // Hide loader
  static void hide() {
    if (_ref != null) {
      _ref!.read(loaderProvider.notifier).toggleLoader(canShow: false);
    }
  }

  // Check if loader is currently showing
  static bool get isShowing {
    if (_ref != null) {
      return _ref!.read(loaderProvider).canShowLoader;
    }
    return false;
  }
}
