import 'dart:async';

import 'package:common_widgets/utils/loader_util.dart';
import 'package:common_widgets/utils/navigation_util.dart';
import 'package:common_widgets/widgets/alert.dart';
import 'package:flutter/material.dart';

Future<void> manipulate(
  BuildContext context,
  FutureOr<void> Function() todo, {
  FutureOr<void> Function()? onError,
  bool forceGoBackToPreviousScreen = true,
}) async {
  LoaderUtils.show();
  try {
    await todo();
  } catch (e) {
    Alert.danger(
      message: e.toString(),
      onPressDefault: (_) async {
        await Future.delayed(Duration.zero);

        if (forceGoBackToPreviousScreen == true) {
          back();
        } else {
          onError?.call();
        }
      },
    );
  } finally {
    LoaderUtils.hide();
  }
}
