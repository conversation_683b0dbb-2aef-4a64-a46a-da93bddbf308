import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Utility class for navigating to main project routes from cooler_verify screens
class MainProjectNavigation {
  /// Navigate to main project home
  static void goToHome(BuildContext context) {
    GoRouter.of(context).go('/home');
  }
  
  /// Navigate to main project login
  static void goToLogin(BuildContext context) {
    GoRouter.of(context).go('/login');
  }
  
  /// Navigate to main project init/splash (your app's starting point)
  static void goToInit(BuildContext context) {
    GoRouter.of(context).go('/init');
  }
  
  /// Navigate to main project store list (different from cooler verify store list)
  static void goToMainStoreList(BuildContext context) {
    GoRouter.of(context).go('/store');
  }
  
  /// Navigate to main project overview
  static void goToOverview(BuildContext context) {
    GoRouter.of(context).go('/overview');
  }
  
  /// Navigate to main project account
  static void goToAccount(BuildContext context) {
    GoRouter.of(context).go('/account');
  }
  
  /// Go back to previous screen with smart fallback
  static void goBack(BuildContext context) {
    if (GoRouter.of(context).canPop()) {
      GoRouter.of(context).pop();
    } else {
      // If can't pop, go to init as fallback (your app's starting point)
      goToInit(context);
    }
  }
  
  /// Force navigation to a safe main project route
  static void goToSafeRoute(BuildContext context) {
    // Try init first (your app's starting point)
    try {
      GoRouter.of(context).go('/init');
    } catch (e) {
      // If even init fails, try home
      try {
        GoRouter.of(context).go('/home');
      } catch (e2) {
        // Last resort - just pop if possible
        if (GoRouter.of(context).canPop()) {
          GoRouter.of(context).pop();
        }
      }
    }
  }
} 