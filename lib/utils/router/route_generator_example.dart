import 'package:common_widgets/utils/router/router_util.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Example showing how to use the route generator
/// 
/// This demonstrates how to integrate the route generation
/// into your Flutter app development workflow.
class RouteGeneratorExample {
  
  /// Example 1: Generate routes programmatically
  static Future<void> generateRoutesExample() async {
    print('=== Route Generation Example ===');
    
    // Generate routes from lib/pages directory
    await RouterUtil.generateRoutesToFile(
      'lib/pages',
      outputPath: 'lib/routes.dart',
    );
    
    print('Routes generated successfully!');
    print('Now you can use the generated routes in your app.');
  }

  /// Example 2: Create a build script
  /// 
  /// Create this as a separate file: scripts/generate_routes.dart
  static Future<void> buildScriptExample(List<String> args) async {
    print('🚀 Pre-build: Generating routes...');
    
    await RouterUtil.generateRoutesToFile(
      'lib/pages',
      outputPath: 'lib/routes.dart',
    );
    
    print('✅ Routes generated, ready to build!');
  }
}

/// Example app showing how to use generated routes
class ExampleApp extends StatelessWidget {
  const ExampleApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'Route Generator Example',
      // This would use the generated router from routes.dart
      // routerConfig: appRouter,
      // For now, we'll use a simple router
      routerConfig: _createExampleRouter(),
    );
  }

  // Example router configuration (this would be generated)
  static final _router = GoRouter(
    routes: [
      GoRoute(
        path: '/',
        builder: (context, state) => const ExampleHomePage(),
      ),
      GoRoute(
        path: '/profile',
        builder: (context, state) => const ExampleProfilePage(),
      ),
      GoRoute(
        path: '/user/:id',
        builder: (context, state) {
          final id = state.pathParameters['id'] ?? '';
          return ExampleUserPage(id: id);
        },
      ),
    ],
  );

  static GoRouter _createExampleRouter() => _router;
}

/// Example pages that would be in lib/pages/

// lib/pages/index.dart
class ExampleHomePage extends StatelessWidget {
  const ExampleHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Home')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('Welcome to the Home Page!'),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => context.go('/profile'),
              child: const Text('Go to Profile'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: () => context.go('/user/123'),
              child: const Text('Go to User 123'),
            ),
          ],
        ),
      ),
    );
  }
}

// lib/pages/profile.dart
class ExampleProfilePage extends StatelessWidget {
  const ExampleProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Profile')),
      body: const Center(
        child: Text('This is the Profile Page'),
      ),
    );
  }
}

// lib/pages/user/[id].dart
class ExampleUserPage extends StatelessWidget {
  final String id;
  
  const ExampleUserPage({super.key, required this.id});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('User: $id')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('User ID: $id', style: const TextStyle(fontSize: 24)),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => context.go('/'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    );
  }
}

/// Example of a pre-build script
/// 
/// Save this as scripts/pre_build.dart and run it before building:
/// dart run scripts/pre_build.dart
void main(List<String> args) async {
  await RouteGeneratorExample.buildScriptExample(args);
}

/// Build commands you can use:
/// 
/// ```bash
/// # Generate routes and run app
/// dart run bin/generate_routes.dart lib/pages && flutter run
/// 
/// # Generate routes and build APK
/// dart run bin/generate_routes.dart lib/pages && flutter build apk
/// 
/// # Generate routes and build web
/// dart run bin/generate_routes.dart lib/pages && flutter build web
/// ```
/// 
/// Example pubspec.yaml scripts section:
/// ```yaml
/// scripts:
///   generate-routes: dart run bin/generate_routes.dart lib/pages
///   build-with-routes: dart run bin/generate_routes.dart lib/pages && flutter build apk
///   dev: dart run bin/generate_routes.dart lib/pages && flutter run
/// ```
