import 'dart:io';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class RouterUtil {
  static List<RouteBase> routes = [];
  
  static GoRouter router = GoRouter(routes: routes);

  /// Generates routes from a directory structure and writes them to a routes.dart file
  ///
  /// This method scans a directory for .dart files and generates a routes.dart file
  /// with GoRouter routes based on the file structure.
  ///
  /// [directoryPath] - The path to the directory containing route files
  /// [outputPath] - Where to write the generated routes.dart file (defaults to 'lib/routes.dart')
  /// [rootPath] - Optional root path for route paths (defaults to '/')
  /// [fileExtension] - File extension to look for (defaults to '.dart')
  ///
  /// Example usage:
  /// ```dart
  /// await RouterUtil.generateRoutesToFile(
  ///   'lib/pages',
  ///   outputPath: 'lib/routes.dart',
  /// );
  /// ```
  static Future<void> generateRoutesToFile(
    String directoryPath, {
    String outputPath = 'lib/routes.dart',
    String rootPath = '/',
    String fileExtension = '.dart',
  }) async {
    print('🔄 Generating routes from directory: $directoryPath');
    
    final List<RouteInfo> routeInfos = [];
    final Directory directory = Directory(directoryPath);

    if (!directory.existsSync()) {
      print('❌ Directory $directoryPath does not exist');
      return;
    }

    // Scan directory and collect route information
    _scanDirectoryForCodeGeneration(
      directory,
      routeInfos,
      rootPath,
      fileExtension,
      directoryPath,
    );

    // Generate the routes.dart file content
    final String generatedCode = _generateRoutesFileContent(routeInfos);

    // Write to file
    final File outputFile = File(outputPath);
    await outputFile.parent.create(recursive: true);
    await outputFile.writeAsString(generatedCode);

    print('✅ Generated ${routeInfos.length} routes to $outputPath');
    print('📋 Routes generated:');
    for (final route in routeInfos) {
      print('   ${route.path} -> ${route.className}');
    }
  }

  /// Scans directory and collects route information for code generation
  static void _scanDirectoryForCodeGeneration(
    Directory directory,
    List<RouteInfo> routeInfos,
    String currentPath,
    String fileExtension,
    String basePath,
  ) {
    final List<FileSystemEntity> entities = directory.listSync();

    // Separate files and directories
    final List<File> files = [];
    final List<Directory> directories = [];

    for (final entity in entities) {
      if (entity is File && entity.path.endsWith(fileExtension)) {
        files.add(entity);
      } else if (entity is Directory) {
        directories.add(entity);
      }
    }

    // Process files first
    for (final file in files) {
      final String fileName = _getFileName(file.path);
      String routePath = _buildRoutePath(currentPath, fileName);

      // Handle special cases
      if (fileName == 'index') {
        routePath = currentPath == '/' ? '/' : currentPath;
      } else if (fileName.startsWith('[') && fileName.endsWith(']')) {
        // Dynamic route parameter
        final String paramName = fileName.substring(1, fileName.length - 1);
        routePath = '$currentPath/:$paramName';
      }

      // Get import path relative to lib/
      final String importPath = _getImportPath(file.path, basePath);
      final String className = _extractClassName(file);

      routeInfos.add(RouteInfo(
        path: routePath,
        name: _generateRouteName(routePath),
        importPath: importPath,
        className: className,
        fileName: fileName,
        isDynamic: fileName.startsWith('[') && fileName.endsWith(']'),
      ));
    }

    // Process directories recursively
    for (final directory in directories) {
      final String dirName = _getDirName(directory.path);
      final String newPath = currentPath == '/'
          ? '/$dirName'
          : '$currentPath/$dirName';

      _scanDirectoryForCodeGeneration(
        directory,
        routeInfos,
        newPath,
        fileExtension,
        basePath,
      );
    }
  }

  /// Generates the content for routes.dart file
  static String _generateRoutesFileContent(List<RouteInfo> routeInfos) {
    final StringBuffer buffer = StringBuffer();

    // File header
    buffer.writeln('// GENERATED CODE - DO NOT MODIFY BY HAND');
    buffer.writeln('// Generated by RouterUtil.generateRoutesToFile()');
    buffer.writeln('// Generated on: ${DateTime.now().toIso8601String()}');
    buffer.writeln();

    // Imports
    buffer.writeln("import 'package:flutter/material.dart';");
    buffer.writeln("import 'package:go_router/go_router.dart';");
    buffer.writeln();

    // Import all page files
    final Set<String> imports = routeInfos.map((r) => r.importPath).toSet();
    for (final import in imports) {
      buffer.writeln("import '$import';");
    }
    buffer.writeln();

    // Routes list
    buffer.writeln('/// Generated routes from directory structure');
    buffer.writeln('final List<RouteBase> appRoutes = [');

    for (final route in routeInfos) {
      buffer.writeln('  GoRoute(');
      buffer.writeln("    path: '${route.path}',");
      buffer.writeln("    name: '${route.name}',");
      buffer.writeln('    builder: (context, state) {');
      
      if (route.isDynamic) {
        // Handle dynamic routes with parameters
        final paramName = route.fileName.substring(1, route.fileName.length - 1);
        buffer.writeln("      final $paramName = state.pathParameters['$paramName'] ?? '';");
        buffer.writeln('      return ${route.className}($paramName: $paramName);');
      } else {
        // Static routes
        buffer.writeln('      return const ${route.className}();');
      }
      
      buffer.writeln('    },');
      buffer.writeln('  ),');
    }

    buffer.writeln('];');
    buffer.writeln();

    // GoRouter instance
    buffer.writeln('/// Generated GoRouter instance');
    buffer.writeln('final GoRouter appRouter = GoRouter(');
    buffer.writeln('  routes: appRoutes,');
    buffer.writeln('  initialLocation: \'/\',');
    buffer.writeln('  errorBuilder: (context, state) => Scaffold(');
    buffer.writeln('    appBar: AppBar(title: const Text(\'Page Not Found\')),');
    buffer.writeln('    body: Center(');
    buffer.writeln('      child: Column(');
    buffer.writeln('        mainAxisAlignment: MainAxisAlignment.center,');
    buffer.writeln('        children: [');
    buffer.writeln('          const Icon(Icons.error_outline, size: 64),');
    buffer.writeln('          const SizedBox(height: 16),');
    buffer.writeln('          Text(\'Route not found: \${state.location}\'),');
    buffer.writeln('          const SizedBox(height: 16),');
    buffer.writeln('          ElevatedButton(');
    buffer.writeln('            onPressed: () => context.go(\'/\'),');
    buffer.writeln('            child: const Text(\'Go Home\'),');
    buffer.writeln('          ),');
    buffer.writeln('        ],');
    buffer.writeln('      ),');
    buffer.writeln('    ),');
    buffer.writeln('  ),');
    buffer.writeln(');');

    return buffer.toString();
  }

  /// Extracts the class name from a Dart file
  static String _extractClassName(File file) {
    try {
      final String content = file.readAsStringSync();
      final RegExp classRegex = RegExp(r'class\s+(\w+)\s+extends\s+StatelessWidget');
      final Match? match = classRegex.firstMatch(content);
      if (match != null) {
        return match.group(1)!;
      }
      
      // Try StatefulWidget
      final RegExp statefulRegex = RegExp(r'class\s+(\w+)\s+extends\s+StatefulWidget');
      final Match? statefulMatch = statefulRegex.firstMatch(content);
      if (statefulMatch != null) {
        return statefulMatch.group(1)!;
      }
      
      // Fallback to filename
      return '${_getFileName(file.path).split('_').map((word) => 
        word.isEmpty ? '' : word[0].toUpperCase() + word.substring(1)).join('')}Page';
    } catch (e) {
      // Fallback to filename-based class name
      return '${_getFileName(file.path).split('_').map((word) => 
        word.isEmpty ? '' : word[0].toUpperCase() + word.substring(1)).join('')}Page';
    }
  }

  /// Gets the import path relative to lib/
  static String _getImportPath(String filePath, String basePath) {
    // Convert to relative path from lib/
    String relativePath = filePath;
    
    // If the file is in the project, make it relative to lib/
    if (filePath.contains('/lib/')) {
      relativePath = filePath.split('/lib/').last;
    } else {
      // If not in lib/, try to make it relative to the base path
      relativePath = filePath.replaceFirst(basePath, '').replaceFirst(RegExp(r'^/'), '');
    }
    
    return relativePath;
  }

  /// Creates a command-line tool to generate routes
  static Future<void> generateRoutesCommand(List<String> args) async {
    if (args.isEmpty) {
      print('Usage: dart run lib/utils/router/router_util.dart <pages_directory> [output_file]');
      print('Example: dart run lib/utils/router/router_util.dart lib/pages lib/routes.dart');
      return;
    }

    final String pagesDirectory = args[0];
    final String outputFile = args.length > 1 ? args[1] : 'lib/routes.dart';

    await generateRoutesToFile(
      pagesDirectory,
      outputPath: outputFile,
    );
  }

  // ===============================
  // EXISTING RUNTIME METHODS
  // ===============================

  /// Generates routes from a directory structure (runtime version)
  ///
  /// [directoryPath] - The path to the directory containing route files
  /// [rootPath] - Optional root path for route paths (defaults to '/')
  /// [fileExtension] - File extension to look for (defaults to '.dart')
  ///
  /// Example directory structure:
  /// ```
  /// pages/
  ///   ├── home.dart          -> /home
  ///   ├── profile.dart       -> /profile
  ///   ├── settings/
  ///   │   ├── index.dart     -> /settings
  ///   │   └── account.dart   -> /settings/account
  ///   └── user/
  ///       └── [id].dart      -> /user/:id
  /// ```
  static List<RouteBase> generateRoutesFromDirectory(
    String directoryPath, {
    String rootPath = '/',
    String fileExtension = '.dart',
    Map<String, Widget Function(BuildContext, GoRouterState)>? customBuilders,
  }) {
    final List<RouteBase> generatedRoutes = [];
    final Directory directory = Directory(directoryPath);

    if (!directory.existsSync()) {
      print('Warning: Directory $directoryPath does not exist');
      return generatedRoutes;
    }

    _scanDirectoryRuntime(
      directory,
      generatedRoutes,
      rootPath,
      fileExtension,
      customBuilders ?? {},
    );

    return generatedRoutes;
  }

  static void _scanDirectoryRuntime(
    Directory directory,
    List<RouteBase> routes,
    String currentPath,
    String fileExtension,
    Map<String, Widget Function(BuildContext, GoRouterState)> customBuilders,
  ) {
    final List<FileSystemEntity> entities = directory.listSync();

    // Separate files and directories
    final List<File> files = [];
    final List<Directory> directories = [];

    for (final entity in entities) {
      if (entity is File && entity.path.endsWith(fileExtension)) {
        files.add(entity);
      } else if (entity is Directory) {
        directories.add(entity);
      }
    }

    // Process files first
    for (final file in files) {
      final String fileName = _getFileName(file.path);
      String routePath = _buildRoutePath(currentPath, fileName);

      // Handle special cases
      if (fileName == 'index') {
        routePath = currentPath == '/' ? '/' : currentPath;
      } else if (fileName.startsWith('[') && fileName.endsWith(']')) {
        // Dynamic route parameter
        final String paramName = fileName.substring(1, fileName.length - 1);
        routePath = '$currentPath/:$paramName';
      }

      // Create the route
      final route = GoRoute(
        path: routePath,
        name: _generateRouteName(routePath),
        builder: (context, state) {
          // Check if custom builder exists
          final String routeKey = file.path;
          if (customBuilders.containsKey(routeKey)) {
            return customBuilders[routeKey]!(context, state);
          }

          // Default placeholder widget
          return _buildDefaultPage(routePath, fileName);
        },
      );

      routes.add(route);
    }

    // Process directories recursively
    for (final directory in directories) {
      final String dirName = _getDirName(directory.path);
      final String newPath = currentPath == '/'
          ? '/$dirName'
          : '$currentPath/$dirName';

      final List<RouteBase> childRoutes = [];
      _scanDirectoryRuntime(
        directory,
        childRoutes,
        newPath,
        fileExtension,
        customBuilders,
      );

      if (childRoutes.isNotEmpty) {
        // Check if there's an index file in this directory
        final bool hasIndexRoute = childRoutes.any(
          (route) => route is GoRoute && route.path == newPath,
        );

        if (hasIndexRoute) {
          // Add child routes to the main routes list
          routes.addAll(childRoutes);
        } else {
          // Create a parent route with child routes
          final parentRoute = GoRoute(
            path: newPath,
            name: _generateRouteName(newPath),
            builder: (context, state) => _buildDefaultPage(newPath, dirName),
            routes: childRoutes,
          );
          routes.add(parentRoute);
        }
      }
    }
  }

  static String _getFileName(String filePath) {
    final String fileName = filePath.split('/').last;
    return fileName.split('.').first;
  }

  static String _getDirName(String dirPath) {
    return dirPath.split('/').last;
  }

  static String _buildRoutePath(String currentPath, String fileName) {
    if (currentPath == '/') {
      return '/$fileName';
    }
    return '$currentPath/$fileName';
  }

  static String _generateRouteName(String path) {
    return path.replaceAll('/', '_').replaceAll(':', '').substring(1);
  }

  static Widget _buildDefaultPage(String routePath, String fileName) {
    return Scaffold(
      appBar: AppBar(title: Text(_formatTitle(fileName))),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Route: $routePath',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text(
              'This is a placeholder page.\nImplement your custom builder to replace this.',
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  static String _formatTitle(String fileName) {
    if (fileName.startsWith('[') && fileName.endsWith(']')) {
      return 'Dynamic Route';
    }
    return fileName
        .split('_')
        .map(
          (word) =>
              word.isEmpty ? '' : word[0].toUpperCase() + word.substring(1),
        )
        .join(' ');
  }

  /// Helper method to update routes with generated routes
  static void updateRoutesFromDirectory(
    String directoryPath, {
    String rootPath = '/',
    String fileExtension = '.dart',
    required GlobalKey<NavigatorState> navigatorKey,
    Map<String, Widget Function(BuildContext, GoRouterState)>? customBuilders,
  }) {
    final generatedRoutes = generateRoutesFromDirectory(
      directoryPath,
      rootPath: rootPath,
      fileExtension: fileExtension,
      customBuilders: customBuilders,
    );

    routes.addAll(generatedRoutes);

    // Recreate the router with updated routes
    router = GoRouter(routes: routes, navigatorKey: navigatorKey);
  }
}

/// Route information for code generation
class RouteInfo {
  final String path;
  final String name;
  final String importPath;
  final String className;
  final String fileName;
  final bool isDynamic;

  RouteInfo({
    required this.path,
    required this.name,
    required this.importPath,
    required this.className,
    required this.fileName,
    required this.isDynamic,
  });
}

// Command line entry point for generating routes
void main(List<String> args) async {
  await RouterUtil.generateRoutesCommand(args);
} 