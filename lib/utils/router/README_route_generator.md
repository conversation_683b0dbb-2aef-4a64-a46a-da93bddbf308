# Route Generator

This utility generates GoRouter routes from your directory structure, similar to Next.js file-based routing.

## How it works

The route generator scans a directory (typically `lib/pages`) for `.dart` files and generates a `routes.dart` file with all the routes configured.

## Usage

### 1. Generate Routes Command

Run this command before building or running your app:

```bash
# Generate routes from lib/pages directory
dart run bin/generate_routes.dart lib/pages

# Or specify custom output path
dart run bin/generate_routes.dart lib/pages lib/generated_routes.dart
```

### 2. Use Generated Routes in Your App

After generating routes, use them in your `MaterialApp.router`:

```dart
import 'package:flutter/material.dart';
import 'routes.dart'; // Import the generated routes

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'My App',
      routerConfig: appRouter, // Use the generated router
    );
  }
}
```

## Directory Structure → Routes

The generator follows these conventions:

### Basic Routes
```
lib/pages/
├── home.dart          → /home
├── profile.dart       → /profile
└── settings.dart      → /settings
```

### Index Routes
```
lib/pages/
├── index.dart         → /
└── settings/
    └── index.dart     → /settings
```

### Nested Routes
```
lib/pages/
└── settings/
    ├── index.dart     → /settings
    ├── account.dart   → /settings/account
    └── privacy.dart   → /settings/privacy
```

### Dynamic Routes
```
lib/pages/
├── user/
│   └── [id].dart      → /user/:id
├── shop/
│   └── [category].dart → /shop/:category
└── blog/
    └── [slug].dart    → /blog/:slug
```

**Important:** Dynamic parameters work only for **files**, not directories. 
- ✅ `user/[id].dart` → `/user/:id`
- ❌ `user/[id]/profile.dart` → `/user/[id]/profile` (directory named `[id]`, not dynamic)

## Page File Requirements

Each page file should export a Flutter widget class:

### Static Pages
```dart
// lib/pages/home.dart
import 'package:flutter/material.dart';

class HomePage extends StatelessWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Home')),
      body: const Center(
        child: Text('Welcome to Home Page!'),
      ),
    );
  }
}
```

### Dynamic Pages
```dart
// lib/pages/user/[id].dart
import 'package:flutter/material.dart';

class UserPage extends StatelessWidget {
  final String id;
  
  const UserPage({Key? key, required this.id}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('User: $id')),
      body: Center(
        child: Text('User ID: $id'),
      ),
    );
  }
}
```

## Generated Routes File

The generator creates a `routes.dart` file like this:

```dart
// GENERATED CODE - DO NOT MODIFY BY HAND
// Generated by generate_routes.dart
// Generated on: 2024-01-01T00:00:00.000Z

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import 'pages/home.dart';
import 'pages/user/[id].dart';

/// Generated routes from directory structure
final List<RouteBase> appRoutes = [
  GoRoute(
    path: '/home',
    name: 'home',
    builder: (context, state) {
      return const HomePage();
    },
  ),
  GoRoute(
    path: '/user/:id',
    name: 'user_id',
    builder: (context, state) {
      final id = state.pathParameters['id'] ?? '';
      return UserPage(id: id);
    },
  ),
];

/// Generated GoRouter instance
final GoRouter appRouter = GoRouter(
  routes: appRoutes,
  initialLocation: '/',
  errorBuilder: (context, state) => Scaffold(
    appBar: AppBar(title: const Text('Page Not Found')),
    body: Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64),
          const SizedBox(height: 16),
          Text('Route not found: ${state.location}'),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => context.go('/'),
            child: const Text('Go Home'),
          ),
        ],
      ),
    ),
  ),
);
```

## Integration with Build Process

### Option 1: Manual Generation
Run the command manually before building:
```bash
dart run bin/generate_routes.dart lib/pages && flutter run
```

### Option 2: Build Script
Create a build script that generates routes automatically:

```bash
#!/bin/bash
# build.sh
echo "Generating routes..."
dart run bin/generate_routes.dart lib/pages
echo "Building app..."
flutter build apk
```

### Option 3: VS Code Task
Add to `.vscode/tasks.json`:
```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Generate Routes",
      "type": "shell",
      "command": "dart",
      "args": ["run", "bin/generate_routes.dart", "lib/pages"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      }
    }
  ]
}
```

## Navigation

Use GoRouter's navigation methods:

```dart
// Navigate to routes
context.go('/home');
context.go('/user/123');
context.go('/shop/electronics/456');

// Navigate with parameters
context.goNamed('user_id', pathParameters: {'id': '123'});

// Push routes
context.push('/settings/account');
```

## Benefits

- **Type Safety**: Generated routes are type-safe
- **Build-time Generation**: Routes are generated once, not at runtime
- **File-based Routing**: Intuitive directory structure
- **Dynamic Routes**: Support for parameterized routes
- **Error Handling**: Built-in 404 error page
- **Performance**: No runtime route scanning

## Troubleshooting

### Class Not Found
Make sure your page files export a class that extends `StatelessWidget` or `StatefulWidget`:
```dart
class HomePage extends StatelessWidget { ... }
```

### Import Errors
Ensure all page files are in the scanned directory and have proper class exports.

### Route Conflicts
Avoid having both `index.dart` and a file with the same name as the directory.

### Dynamic Route Parameters
Dynamic routes require the parameter to be passed to the widget constructor:
```dart
class UserPage extends StatelessWidget {
  final String id; // Must match the parameter name
  const UserPage({required this.id});
}
``` 