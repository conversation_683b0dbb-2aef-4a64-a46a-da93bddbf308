// import 'dart:async';
// import 'dart:convert';
// import 'dart:io';
// import 'dart:math';
// import 'dart:ui' as ui;

// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_image_compress/flutter_image_compress.dart';
// import 'package:image/image.dart' as img;
// import 'package:permission_handler/permission_handler.dart';
// import 'package:pl_coke_cooler/core/extensions/string_extension.dart';
// import 'package:common_widgets/widgets/alert.dart';

// /// Main function to draw text on image
// Future<ByteData?> drawText(
//   Uint8List imageBytes,
//   List<String> texts,
//   bool inverseRendering,
//   int imageWidth,
//   int imageHeight,
//   double textSpacing,
//   double fontSize,
//   String textColor,
// ) async {
//   // Scale down image if too large for better performance
//   const int maxDimension = 1024;
//   if (imageWidth > maxDimension || imageHeight > maxDimension) {
//     final double scale = maxDimension / max(imageWidth, imageHeight);
//     imageWidth = (imageWidth * scale).round();
//     imageHeight = (imageHeight * scale).round();

//     // Resize image bytes
//     final resizedBytes = await FlutterImageCompress.compressWithList(
//       imageBytes,
//       minHeight: imageHeight,
//       minWidth: imageWidth,
//       quality: 85,
//     );
//     imageBytes = resizedBytes;
//   }

//   // Calculate text metrics in isolate
//   final textMetrics = await compute<Map<String, dynamic>, List<Map<String, dynamic>>>(
//     _calculateTextMetricsInIsolate,
//     {
//       'texts': texts,
//       'fontSize': fontSize,
//       'maxWidth': imageWidth - 60.0,
//     },
//   );

//   // Now do UI operations in main isolate
//   return _drawTextOnMainIsolate(
//     imageBytes: imageBytes,
//     texts: texts,
//     inverseRendering: inverseRendering,
//     textSpacing: textSpacing,
//     fontSize: fontSize,
//     textColor: textColor,
//     imageWidth: imageWidth,
//     imageHeight: imageHeight,
//     textMetrics: textMetrics,
//   );
// }

// /// UI operations in main isolate for drawing text on image
// Future<ByteData?> _drawTextOnMainIsolate({
//   required Uint8List imageBytes,
//   required List<String> texts,
//   required bool inverseRendering,
//   required double textSpacing,
//   required double fontSize,
//   required String textColor,
//   required int imageWidth,
//   required int imageHeight,
//   required List<Map<String, dynamic>> textMetrics,
// }) async {
//   ui.PictureRecorder recorder = ui.PictureRecorder();
//   Canvas canvas = Canvas(recorder);

//   try {
//     // For iOS, pre-process the image using the image package
//     Uint8List processedBytes = imageBytes;
//     if (Platform.isIOS) {
//       final img.Image? decodedImage = img.decodeImage(imageBytes);
//       if (decodedImage != null) {
//         // Convert to RGBA format and ensure proper color space
//         final img.Image rgbaImage = img.Image.from(decodedImage);
//         processedBytes = Uint8List.fromList(img.encodePng(rgbaImage));
//       }
//     }

//     // Decode and draw original image
//     final ui.Image originalImage = await decodeImageFromList(processedBytes);

//     Paint paint = Paint()
//       ..colorFilter = null
//       ..blendMode = BlendMode.srcOver
//       ..isAntiAlias = true;

//     canvas.drawImage(originalImage, Offset.zero, paint);
//     originalImage.dispose();

//     if (texts.isEmpty) {
//       ui.Picture picture = recorder.endRecording();
//       ui.Image imgUI = await picture.toImage(imageWidth, imageHeight);
//       ByteData? byteData = await imgUI.toByteData(format: ui.ImageByteFormat.png);
//       imgUI.dispose();
//       picture.dispose();
//       return byteData;
//     }

//     List<String> lstText = texts.toList();
//     if (inverseRendering) {
//       lstText = lstText.reversed.toList();
//     }

//     double totalHeight = 0;
//     for (var metric in textMetrics) {
//       totalHeight += (metric['height'] as double) + textSpacing;
//     }
//     totalHeight -= textSpacing; // Remove last spacing

//     // Calculate starting Y position
//     double startY = imageHeight - 120 - totalHeight;

//     // Draw all text at once
//     for (int i = 0; i < lstText.length; i++) {
//       final metric = textMetrics[i];
//       const x = 60.0;
//       final y = startY + ((metric['height'] as double) + textSpacing) * i;

//       TextPainter textPainter = TextPainter(
//         text: TextSpan(
//           text: lstText[i],
//           style: TextStyle(
//             color: textColor.toColor(),
//             fontSize: fontSize,
//           ),
//         ),
//         textDirection: TextDirection.ltr,
//       );

//       textPainter.layout(maxWidth: imageWidth - 60);
//       textPainter.paint(canvas, Offset(x, y));
//       textPainter.dispose();
//     }

//     // Create final image
//     ui.Picture picture = recorder.endRecording();
//     ui.Image imgUI = await picture.toImage(imageWidth, imageHeight);
//     ByteData? byteData = await imgUI.toByteData(format: ui.ImageByteFormat.png);

//     // Clean up resources
//     imgUI.dispose();
//     picture.dispose();

//     return byteData;
//   } catch (e) {
//     recorder.endRecording().dispose();
//     return null;
//   }
// }

// /// Calculate text metrics in isolate (no UI operations)
// List<Map<String, dynamic>> _calculateTextMetricsInIsolate(Map<String, dynamic> params) {
//   final List<String> texts = params['texts'];
//   final double fontSize = params['fontSize'];
//   final double maxWidth = params['maxWidth'];

//   return texts.map((text) {
//     // Calculate text height without using TextPainter
//     final int lineCount = (text.length * fontSize / maxWidth).ceil();
//     final double height = lineCount * fontSize * 1.2; // Approximate line height

//     return {
//       'height': height,
//       'lineCount': lineCount,
//     };
//   }).toList();
// }

// // =============================================
// // Photo Taking Functions
// // =============================================

// /// Optimized version of takePhoto that uses isolates for better performance
// Future<String?> takePhotoOptimized({
//   required List<String> texts,
//   double textSpacing = 32,
//   bool inverseRendering = true,
//   CameraDevice cameraDevice = CameraDevice.rear,
//   bool saveToGallery = true,
// }) async {
//   try {
//     // Check permissions sequentially on main thread
//     if (saveToGallery) {
//       final hasStoragePermission = await _checkStoragePermission();
//       if (!hasStoragePermission) {
//         return null;
//       }
//     }

//     final hasCameraPermission = await _checkCameraPermission();
//     if (!hasCameraPermission) {
//       return null;
//     }

//     // Take photo with optimized settings
//     final photo = await ImagePicker().pickImage(
//       source: ImageSource.camera,
//       imageQuality: 85,
//       maxHeight: 1024, // Reduced from 2048
//       maxWidth: 1024, // Reduced from 2048
//       requestFullMetadata: true,
//       preferredCameraDevice: cameraDevice,
//     );

//     if (photo == null) {
//       await Alert.danger(message: 'error.canNotTakePhoto'.t);
//       return null;
//     }

//     // Process image in isolate for heavy operations
//     final processedImageData = await compute<Map<String, dynamic>, Map<String, dynamic>?>(
//       _processImageInIsolate,
//       {
//         'imagePath': photo.path,
//         'texts': texts,
//         'inverseRendering': inverseRendering,
//         'textSpacing': textSpacing,
//         'textColor': globalConfig[SystemConfigKey.textOnImageColor],
//       },
//     );

//     if (processedImageData == null) {
//       await Alert.danger(message: 'error.canNotTakePhoto'.t);
//       return null;
//     }

//     // Draw text on image in main isolate (UI operations)
//     final byteData = await drawText(
//       processedImageData['imageBytes'],
//       processedImageData['texts'],
//       processedImageData['inverseRendering'],
//       processedImageData['imageWidth'],
//       processedImageData['imageHeight'],
//       processedImageData['textSpacing'],
//       d32,
//       processedImageData['textColor'],
//     );

//     if (byteData == null) {
//       await Alert.danger(message: 'error.canNotTakePhoto'.t);
//       return null;
//     }

//     // Save and compress the final image
//     final imageDir = await getImageDirectory();
//     final String newPath = '${imageDir.path}/new_${path.basename(photo.path)}';
//     final newFile = File(newPath);

//     if (!newFile.existsSync()) {
//       await newFile.create();
//     }

//     // Write the processed image
//     await newFile.writeAsBytes(byteData.buffer.asUint8List());

//     // Delete original photo
//     await File(photo.path).delete();

//     // Compress image with optimized settings
//     final xfile = await FlutterImageCompress.compressWithFile(
//       newFile.absolute.path,
//       quality: 100,
//     );

//     if (xfile == null) {
//       return null;
//     }

//     await newFile.writeAsBytes(xfile);

//     // Add EXIF data
//     final exif = await Exif.fromPath(newFile.path);
//     await exif.writeAttribute('UserComment', AppConfig.exifKey);

//     if (!saveToGallery) {
//       return newPath;
//     }

//     return await saveImageToGallery(newPath);
//   } catch (e) {
//     await Alert.danger(message: '${'error.canNotTakePhoto'.t}, mã lỗi : $e');
//     return null;
//   }
// }

// /// Process image in isolate
// Future<Map<String, dynamic>?> _processImageInIsolate(Map<String, dynamic> params) async {
//   try {
//     final String imagePath = params['imagePath'];
//     final List<String> texts = params['texts'];
//     final bool inverseRendering = params['inverseRendering'];
//     final double textSpacing = params['textSpacing'];
//     final String textColor = params['textColor'];

//     final file = File(imagePath);
//     if (!file.existsSync()) {
//       return null;
//     }

//     final imageBytes = await file.readAsBytes();
//     final image = img.decodeImage(imageBytes);
//     if (image == null) {
//       return null;
//     }

//     // For iOS, ensure proper color space
//     Uint8List processedBytes = imageBytes;
//     if (Platform.isIOS) {
//       // Create a new image with white background
//       final img.Image newImage = img.Image(width: image.width, height: image.height);
//       // Fill with white
//       for (var y = 0; y < newImage.height; y++) {
//         for (var x = 0; x < newImage.width; x++) {
//           newImage.setPixel(x, y, img.ColorRgb8(255, 255, 255));
//         }
//       }
//       // Copy original image
//       for (var y = 0; y < image.height; y++) {
//         for (var x = 0; x < image.width; x++) {
//           newImage.setPixel(x, y, image.getPixel(x, y));
//         }
//       }
//       processedBytes = Uint8List.fromList(img.encodePng(newImage));
//     }

//     return {
//       'imageBytes': processedBytes,
//       'texts': texts,
//       'inverseRendering': inverseRendering,
//       'textSpacing': textSpacing,
//       'textColor': textColor,
//       'imageWidth': image.width,
//       'imageHeight': image.height,
//     };
//   } catch (e) {
//     return null;
//   }
// }

// // =============================================
// // Permission Helper Functions
// // =============================================

// /// Helper function to check camera permission
// Future<bool> _checkCameraPermission() async {
//   try {
//     PermissionStatus permission = await Permission.camera.request();
//     if (permission != PermissionStatus.granted) {
//       await Alert.danger(
//         message: 'Bạn chưa cấp quyền truy cập camera',
//       );
//       return false;
//     }
//     return true;
//   } catch (e) {
//     await Alert.danger(message: 'Có lỗi xãy ra - $e');
//     return false;
//   }
// }

// // =============================================
// // Utility Functions
// // =============================================

// Future<String?> drawTextToImage(
//     {required String imagePath, required List<String> texts, bool inverseRendering = false, double textSpacing = 16}) async {
//   try {
//     if (texts.isEmpty) {
//       return null;
//     } else if (imagePath.isEmpty == true) {
//       return null;
//     }

//     final file = File(imagePath);

//     if (file.existsSync() == false) {
//       await Alert.danger(message: 'Không tìm thấy file');
//       return null;
//     }

//     final imageBytes = await file.readAsBytes();

//     final image = img.decodeImage(imageBytes);

//     if (image == null) {
//       await Alert.danger(message: 'Ảnh rỗng, vui lòng thử lại');
//       return null;
//     }

//     final byteData = await compute<dynamic, ByteData?>(
//       PhotoHelper.takePhotoWithIsolate,
//       {
//         'imageBytes': imageBytes,
//         'texts': texts,
//         'inverseRendering': inverseRendering,
//         'imageWidth': image.width,
//         'imageHeight': image.height,
//         'textSpacing': textSpacing,
//         'fontSize': 32,
//         'textColor': '#000000',
//       },
//     );

//     if (byteData == null) {
//       await Alert.danger(message: 'Không thể tạo ảnh');
//       return null;
//     }

//     final imageDir = await getImageDirectory();

//     final String newPath = '${imageDir.path}/new_${path.basename(imagePath)}';

//     final newFile = File(newPath);

//     if (newFile.existsSync() == false) {
//       await newFile.create();
//     }

//     await newFile.writeAsBytes(byteData.buffer.asUint8List());

//     await file.delete();

//     final xfile = kIsWeb
//         ? await FlutterImageCompress.compressWithList(
//             await newFile.readAsBytes(),
//             quality: 75,
//           )
//         : await FlutterImageCompress.compressWithFile(
//             newFile.absolute.path,
//             quality: 75,
//           );

//     if (xfile == null) {
//       await Alert.danger(message: 'Không thể nén ảnh, bạn vui lòng thử lại');
//       return '';
//     }

//     await newFile.writeAsBytes(xfile);

//     return newPath;
//   } catch (e) {
//     await Alert.danger(message: 'Có lỗi xãy ra - ${e.toString()}');
//     return null;
//   }
// }

// @pragma('vm:entry-point')
// class PhotoHelper {
//   @pragma('vm:entry-point')
//   static Future<ByteData?> takePhotoWithIsolate(dynamic args) async {
//     return await drawText(
//       args['imageBytes'],
//       args['texts'],
//       args['inverseRendering'],
//       args['imageWidth'],
//       args['imageHeight'],
//       args['textSpacing'],
//       args['fontSize'],
//       args['textColor'],
//     );
//   }
// }

// ///Hàm gen ra một chuỗi UUID
// String generateUUID() {
//   final Random random = Random();
//   final List<int> uuid = List.generate(36, (index) {
//     if (index == 8 || index == 13 || index == 18 || index == 23) {
//       return 45; // ASCII code for hyphen ""-""
//     } else if (index == 14) {
//       return 52; // ASCII code for ""4"" to indicate the version
//     } else if (index == 19) {
//       return 56 + random.nextInt(4); // Randomize the 13th character
//     } else {
//       return 97 + random.nextInt(26); // Randomize lowercase letters
//     }
//   });

//   return String.fromCharCodes(uuid);
// }

// ///Lấy base64 từ đường dẫn hình ảnh
// Future<String> getBase64fromPath(String imagePath) async {
//   File file = File(imagePath);

//   if (file.existsSync() == false) {
//     await Alert.danger(message: 'file.notFound'.t);
//     return '';
//   }

//   final imageBytes = await file.readAsBytes();

//   return base64Encode(imageBytes);
// }

// /// Ghép key của Bill upload
// String getBiphoKey({
//   required String codeStaff,
//   required String codeStore,
//   required String referId,
//   required int customerId,
// }) {
//   final normalizedObj = _normalizeStaffStore(codeStaff: codeStaff, codeStore: codeStore);

//   return '$referId-${normalizedObj['normalizedCodeStaff']}-${normalizedObj['normalizedCodeStore']}-$customerId';
// }

// List<OptionItem> getDays() {
//   return [
//     OptionItem(key: 'wkp.monday'.t, value: 0, extraData: 'Mon'),
//     OptionItem(key: 'wkp.tuesday'.t, value: 1, extraData: 'Tue'),
//     OptionItem(key: 'wkp.wednesday'.t, value: 2, extraData: 'Wed'),
//     OptionItem(key: 'wkp.thursday'.t, value: 3, extraData: 'Thu'),
//     OptionItem(key: 'wkp.friday'.t, value: 4, extraData: 'Fri'),
//     OptionItem(key: 'wkp.saturday'.t, value: 5, extraData: 'Sat'),
//     OptionItem(key: 'wkp.sunday'.t, value: 6, extraData: 'Sun')
//   ];
// }

// /// Returns the directory where images are stored.
// ///
// /// Creates the directory if it doesn't exist.
// ///
// /// Returns a [Future<Directory>] representing the image directory.
// Future<Directory> getImageDirectory() async {
//   final directory = await getApplicationDocumentsDirectory();
//   final imageDir = Directory('${directory.path}/images');

//   if (imageDir.existsSync() == false) {
//     await imageDir.create(recursive: true);
//   }

//   return imageDir;
// }

// double getTextHeight({required String text, required double fontSize, double? maxWidth}) {
//   final TextPainter textPainter = TextPainter(
//     text: TextSpan(
//       text: text,
//       style: TextStyle(fontSize: fontSize),
//     ),
//     maxLines: null,
//     textDirection: TextDirection.ltr,
//   );

//   textPainter.layout(maxWidth: maxWidth ?? double.infinity);

//   return textPainter.height;
// }

// Future onCatchFinally({
//   required Future<void> Function() todo,
//   Future<void> Function()? onFinal,
//   BuildContext? context,
//   bool hasLoader = false,
// }) async {
//   if (hasLoader == true && context == null) {
//     throw Exception('context is null');
//   }

//   LoaderBloc? bloc;

//   if (hasLoader == true && context != null) {
//     bloc = context.getBloc<LoaderBloc>();

//     bloc.add(ToggleLoaderEvent(canShow: true));
//   }

//   try {
//     await todo();
//   } catch (e) {
//     await Alert.danger(message: '${'error.catch'.t} - ${e.toString()}');

//     final apiLog = getIt<LogApi>();

//     apiLog.log(
//       LogResponse(
//         title: '[Flutter-Catch] ${DateTime.now().toLocalFormat(format: "dd/MM/yyyy hh:mm:ss")}',
//         content: e.toString(),
//         sourceName: AppConfig.logSource,
//       ),
//     );
//   } finally {
//     if (hasLoader == true && context != null) {
//       bloc!.add(ToggleLoaderEvent(canShow: false));
//     }
//     if (onFinal != null) {
//       await onFinal();
//     }
//   }
// }

// Future<String?> saveImageToGallery(String processedPhotoPath) async {
//   final file = File(processedPhotoPath);

//   if (file.existsSync() == false) {
//     await Alert.danger(message: 'file.notFound'.t);
//     return null;
//   }

//   try {
//     await Gal.putImage(file.path);
//   } on GalException catch (e) {
//     await Alert.danger(
//       message: e.type.message,
//       onPressDefault: () {
//         if (e.type == GalExceptionType.accessDenied || e.type == GalExceptionType.notEnoughSpace) {
//           AppSettings.openAppSettings();
//         }
//       },
//     );
//     return null;
//   }

//   return processedPhotoPath;
// }

// Future showCommonErrorAlert({String? message, void Function()? onPressDefault}) async {
//   await Alert.danger(message: message ?? 'error.catch'.t, onPressDefault: onPressDefault);
// }
