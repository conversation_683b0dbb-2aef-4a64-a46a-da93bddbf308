// ignore_for_file: avoid_print

import 'dart:async';

import 'package:common_widgets/utils/location_util/location_util.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';

/// Example widget showing how to use LocationUtil in a Flutter app
class LocationExampleWidget extends StatefulWidget {
  const LocationExampleWidget({super.key});

  @override
  State<LocationExampleWidget> createState() => _LocationExampleWidgetState();
}

class _LocationExampleWidgetState extends State<LocationExampleWidget> {
  LocationData? _currentLocation;
  String _status = 'Ready';
  StreamSubscription<LocationData>? _locationSubscription;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Location Util Examples')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text('Status: $_status'),
            const SizedBox(height: 16),
            if (_currentLocation != null) ...[
              const Text('Current Location:'),
              Text('Lat: ${_currentLocation!.latitude}'),
              Text('Lng: ${_currentLocation!.longitude}'),
              Text('Accuracy: ${_currentLocation!.accuracy}m'),
              Text('Time: ${_currentLocation!.timestamp}'),
              const SizedBox(height: 16),
            ],
            ElevatedButton(
              onPressed: _getCurrentLocation,
              child: const Text('Get Current Location'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _getCurrentLocationWithRetry,
              child: const Text('Get Location with Retry'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(onPressed: _checkPermissions, child: const Text('Check Permissions')),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _startLocationStream,
              child: const Text('Start Location Stream'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _stopLocationStream,
              child: const Text('Stop Location Stream'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _calculateDistanceToNYC,
              child: const Text('Calculate Distance to NYC'),
            ),
          ],
        ),
      ),
    );
  }

  /// Example 1: Basic location getting
  Future<void> _getCurrentLocation() async {
    setState(() => _status = 'Getting location...');

    try {
      // Super simple - just one line!
      final location = await LocationUtil.instance.getCurrentLocation();

      setState(() {
        _currentLocation = location;
        _status = 'Location retrieved successfully';
      });

      print('Got location: ${location.latitude}, ${location.longitude}');
    } on LocationException catch (e) {
      setState(() => _status = 'Error: ${e.message}');
      _handleLocationError(e);
    } catch (e) {
      setState(() => _status = 'Unexpected error: $e');
    }
  }

  /// Example 2: Using retry mechanism
  Future<void> _getCurrentLocationWithRetry() async {
    setState(() => _status = 'Getting location with retry...');

    try {
      // Get location with automatic retry on failure
      final location = await LocationUtil.instance.getCurrentLocationWithRetry(
        maxRetries: 3,
        retryDelay: const Duration(seconds: 2),
      );

      setState(() {
        _currentLocation = location;
        _status = 'Location retrieved with retry';
      });
    } on LocationException catch (e) {
      setState(() => _status = 'Failed after retries: ${e.message}');
      _handleLocationError(e);
    }
  }

  /// Example 3: Checking permissions
  Future<void> _checkPermissions() async {
    setState(() => _status = 'Checking permissions...');

    try {
      // Check if we have permission
      final hasPermission = await LocationUtil.instance.hasPermission();
      final permissionStatus = await LocationUtil.instance.checkPermission();
      final isLocationEnabled = await LocationUtil.instance.isLocationEnabled();

      setState(() {
        _status =
            'Has Permission: $hasPermission\n'
            'Permission Status: $permissionStatus\n'
            'Location Enabled: $isLocationEnabled';
      });
    } catch (e) {
      setState(() => _status = 'Error checking permissions: $e');
    }
  }

  /// Example 4: Using location stream
  Future<void> _startLocationStream() async {
    setState(() => _status = 'Starting location stream...');

    try {
      // Get continuous location updates
      _locationSubscription = LocationUtil.instance
          .getLocationStream(
            accuracy: LocationAccuracy.high,
            distanceFilter: 10, // Update every 10 meters
          )
          .listen(
            (location) {
              setState(() {
                _currentLocation = location;
                _status = 'Location stream active';
              });
              print('Stream location: ${location.latitude}, ${location.longitude}');
            },
            onError: (error) {
              setState(() => _status = 'Stream error: $error');
            },
          );
    } catch (e) {
      setState(() => _status = 'Error starting stream: $e');
    }
  }

  /// Example 5: Stop location stream
  void _stopLocationStream() {
    _locationSubscription?.cancel();
    _locationSubscription = null;
    setState(() => _status = 'Location stream stopped');
  }

  /// Example 6: Calculate distance
  Future<void> _calculateDistanceToNYC() async {
    setState(() => _status = 'Calculating distance...');

    try {
      // Calculate distance from current location to New York City
      final distance = await LocationUtil.instance.calculateDistanceFromCurrent(
        targetLatitude: 40.7589, // NYC coordinates
        targetLongitude: -73.9851,
      );

      final distanceKm = (distance / 1000).toStringAsFixed(2);
      setState(() => _status = 'Distance to NYC: ${distanceKm}km');
    } on LocationException catch (e) {
      setState(() => _status = 'Error calculating distance: ${e.message}');
      _handleLocationError(e);
    }
  }

  /// Handle different types of location errors
  void _handleLocationError(LocationException e) {
    switch (e.type) {
      case LocationErrorType.permissionDenied:
        _showPermissionDialog();
        break;
      case LocationErrorType.permissionDeniedForever:
        _showSettingsDialog();
        break;
      case LocationErrorType.serviceDisabled:
        _showLocationServiceDialog();
        break;
      case LocationErrorType.timeout:
        // Maybe retry or show timeout message
        break;
      case LocationErrorType.unknown:
      case LocationErrorType.positionUnavailable:
        // Handle other errors
        break;
    }
  }

  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Location Permission'),
        content: const Text('This app needs location permission to work properly.'),
        actions: [
          TextButton(onPressed: () => Navigator.pop(context), child: const Text('Cancel')),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await LocationUtil.instance.requestPermission();
            },
            child: const Text('Request Permission'),
          ),
        ],
      ),
    );
  }

  void _showSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Location Permission'),
        content: const Text(
          'Location permission is permanently denied. Please enable it in settings.',
        ),
        actions: [
          TextButton(onPressed: () => Navigator.pop(context), child: const Text('Cancel')),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await LocationUtil.instance.openAppSettings();
            },
            child: const Text('Open Settings'),
          ),
        ],
      ),
    );
  }

  void _showLocationServiceDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Location Service'),
        content: const Text('Location services are disabled. Please enable them in settings.'),
        actions: [
          TextButton(onPressed: () => Navigator.pop(context), child: const Text('Cancel')),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await LocationUtil.instance.openLocationSettings();
            },
            child: const Text('Open Settings'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _locationSubscription?.cancel();
    super.dispose();
  }
}

/// Simple usage examples that can be called from anywhere
class LocationUsageExamples {
  /// Example 1: Quick location get
  static Future<void> quickLocationExample() async {
    try {
      // One-liner to get current location
      final location = await LocationUtil.instance.getLocation();
      print('Quick location: ${location.latitude}, ${location.longitude}');
    } catch (e) {
      print('Error: $e');
    }
  }

  /// Example 2: Location with custom settings
  static Future<void> customLocationExample() async {
    try {
      final location = await LocationUtil.instance.getCurrentLocation(
        timeout: const Duration(seconds: 15),
        accuracy: LocationAccuracy.best,
      );
      print('High accuracy location: $location');
    } catch (e) {
      print('Error: $e');
    }
  }

  /// Example 3: Permission check before location
  static Future<void> safeLocationExample() async {
    // Check permission first
    if (await LocationUtil.instance.hasPermission()) {
      final location = await LocationUtil.instance.getLocation();
      print('Safe location: $location');
    } else {
      print('No location permission');
    }
  }

  /// Example 4: Distance calculation
  static Future<void> distanceExample() async {
    try {
      // Calculate distance between two known points
      final distance = LocationUtil.instance.calculateDistance(
        startLatitude: 37.7749, // San Francisco
        startLongitude: -122.4194,
        endLatitude: 40.7589, // New York
        endLongitude: -73.9851,
      );

      print('Distance SF to NYC: ${(distance / 1000).toStringAsFixed(2)}km');
    } catch (e) {
      print('Error: $e');
    }
  }

  /// Example 5: Location stream usage
  static StreamSubscription<LocationData> startLocationTracking() {
    return LocationUtil.instance
        .getLocationStream(
          accuracy: LocationAccuracy.high,
          distanceFilter: 5, // Update every 5 meters
        )
        .listen(
          (location) {
            print('New location: ${location.latitude}, ${location.longitude}');
            // Update your app's state here
          },
          onError: (error) {
            print('Location stream error: $error');
          },
        );
  }

  /// Example 6: Repository pattern usage (for clean architecture)
  static Future<LocationData?> getLocationForRepository() async {
    try {
      return await LocationUtil.instance.getCurrentLocationWithRetry(
        maxRetries: 2,
        timeout: const Duration(seconds: 10),
      );
    } on LocationException catch (e) {
      // Log error, return null, or throw custom domain exception
      print('Location error in repository: ${e.message}');
      return null;
    }
  }
}

/// Example of how to use in a ViewModel/Controller
class LocationViewModel {
  LocationData? _currentLocation;
  bool _isLoading = false;
  String? _error;

  LocationData? get currentLocation => _currentLocation;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> updateLocation() async {
    _isLoading = true;
    _error = null;

    try {
      _currentLocation = await LocationUtil.instance.getCurrentLocation();
    } on LocationException catch (e) {
      _error = e.message;
    } finally {
      _isLoading = false;
    }
  }

  Future<double?> getDistanceTo({required double latitude, required double longitude}) async {
    try {
      return await LocationUtil.instance.calculateDistanceFromCurrent(
        targetLatitude: latitude,
        targetLongitude: longitude,
      );
    } catch (e) {
      return null;
    }
  }
}
