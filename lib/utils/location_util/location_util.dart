import 'dart:async';

import 'package:geolocator/geolocator.dart';

/// Location data model
class LocationData {
  final double latitude;
  final double longitude;
  final double? accuracy;
  final double? altitude;
  final DateTime timestamp;

  const LocationData({
    required this.latitude,
    required this.longitude,
    this.accuracy,
    this.altitude,
    required this.timestamp,
  });

  factory LocationData.fromPosition(Position position) {
    return LocationData(
      latitude: position.latitude,
      longitude: position.longitude,
      accuracy: position.accuracy,
      altitude: position.altitude,
      timestamp: position.timestamp,
    );
  }

  @override
  String toString() {
    return 'LocationData(lat: $latitude, lng: $longitude, accuracy: $accuracy)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocationData &&
        other.latitude == latitude &&
        other.longitude == longitude &&
        other.accuracy == accuracy &&
        other.altitude == altitude;
  }

  @override
  int get hashCode {
    return latitude.hashCode ^ longitude.hashCode ^ accuracy.hashCode ^ altitude.hashCode;
  }
}

/// Location utility exceptions
class LocationException implements Exception {
  final String message;
  final LocationErrorType type;

  const LocationException(this.message, this.type);

  @override
  String toString() => 'LocationException: $message';
}

enum LocationErrorType {
  permissionDenied,
  permissionDeniedForever,
  serviceDisabled,
  positionUnavailable,
  timeout,
  unknown,
}

/// Abstract interface for location services (for testing)
abstract class LocationService {
  Future<LocationData> getCurrentLocation({
    Duration timeout = const Duration(seconds: 10),
    LocationAccuracy accuracy = LocationAccuracy.high,
  });

  Future<bool> isLocationServiceEnabled();

  Future<LocationPermission> getLocationPermission();

  Future<LocationPermission> requestLocationPermission();

  Stream<LocationData> getLocationStream({
    LocationAccuracy accuracy = LocationAccuracy.high,
    int distanceFilter = 10,
  });
}

/// Concrete implementation of location service
class LocationServiceImpl implements LocationService {
  @override
  Future<LocationData> getCurrentLocation({
    Duration timeout = const Duration(seconds: 10),
    LocationAccuracy accuracy = LocationAccuracy.high,
  }) async {
    try {
      // Check if location service is enabled first
      final isServiceEnabled = await isLocationServiceEnabled();
      if (!isServiceEnabled) {
        throw const LocationException(
          'Location services are disabled. Please enable location services.',
          LocationErrorType.serviceDisabled,
        );
      }

      // Check and request permissions
      await _ensureLocationPermission();

      // Get current position with specific error handling for web
      final position = await Geolocator.getCurrentPosition(
        locationSettings: LocationSettings(
          accuracy: accuracy,
          timeLimit: timeout,
        ),
      ).timeout(
        timeout,
        onTimeout: () {
          throw const LocationException(
            'Location request timed out. Please try again.',
            LocationErrorType.timeout,
          );
        },
      );

      return LocationData.fromPosition(position);
    } on TimeoutException {
      throw const LocationException(
        'Location request timed out. Please try again.',
        LocationErrorType.timeout,
      );
    } on LocationServiceDisabledException {
      throw const LocationException(
        'Location services are disabled. Please enable location services.',
        LocationErrorType.serviceDisabled,
      );
    } on PermissionDeniedException {
      throw const LocationException(
        'Location permission denied. Please allow location access.',
        LocationErrorType.permissionDenied,
      );
    } on PositionUpdateException catch (e) {
      throw LocationException(
        'Failed to get location updates: ${e.message}',
        LocationErrorType.positionUnavailable,
      );
    } catch (e) {
      // More specific error message for web platform issues
      String errorMessage = 'Failed to get location: ${e.toString()}';

      // Check for common web-specific issues
      if (e.toString().contains('Network location provider') ||
          e.toString().contains('NETWORK_ERROR') ||
          e.toString().contains('POSITION_UNAVAILABLE')) {
        errorMessage = 'Location unavailable. Please check your internet connection and ensure location services are enabled.';
      } else if (e.toString().contains('PERMISSION_DENIED')) {
        errorMessage = 'Location permission denied. Please allow location access in your browser.';
      } else if (e.toString().contains('TIMEOUT')) {
        errorMessage = 'Location request timed out. Please try again.';
      }

      throw LocationException(
        errorMessage,
        LocationErrorType.unknown,
      );
    }
  }

  @override
  Future<bool> isLocationServiceEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  @override
  Future<LocationPermission> getLocationPermission() async {
    return await Geolocator.checkPermission();
  }

  @override
  Future<LocationPermission> requestLocationPermission() async {
    return await Geolocator.requestPermission();
  }

  @override
  Stream<LocationData> getLocationStream({
    LocationAccuracy accuracy = LocationAccuracy.high,
    int distanceFilter = 10,
  }) {
    final locationSettings = LocationSettings(
      accuracy: accuracy,
      distanceFilter: distanceFilter,
    );

    return Geolocator.getPositionStream(locationSettings: locationSettings).map((position) => LocationData.fromPosition(position));
  }

  Future<void> _ensureLocationPermission() async {
    LocationPermission permission = await getLocationPermission();

    if (permission == LocationPermission.denied) {
      permission = await requestLocationPermission();
    }

    if (permission == LocationPermission.denied) {
      throw const LocationException(
        'Location permission denied',
        LocationErrorType.permissionDenied,
      );
    }

    if (permission == LocationPermission.deniedForever) {
      throw const LocationException(
        'Location permission denied forever. Please enable in settings.',
        LocationErrorType.permissionDeniedForever,
      );
    }
  }
}

/// Main Location Utility class - Singleton for easy access
class LocationUtil {
  static LocationUtil? _instance;
  static LocationService? _locationService;

  LocationUtil._();

  /// Get singleton instance
  static LocationUtil get instance {
    _instance ??= LocationUtil._();
    return _instance!;
  }

  /// Initialize with custom location service (useful for testing)
  static void initialize({LocationService? locationService}) {
    _locationService = locationService ?? LocationServiceImpl();
  }

  LocationService get _service {
    _locationService ??= LocationServiceImpl();
    return _locationService!;
  }

  /// Get current location quickly and easily
  ///
  /// Example usage:
  /// ```dart
  /// try {
  ///   final location = await LocationUtil.instance.getCurrentLocation();
  ///   print('Current location: ${location.latitude}, ${location.longitude}');
  /// } catch (e) {
  ///   print('Error getting location: $e');
  /// }
  /// ```
  Future<LocationData> getCurrentLocation({
    Duration timeout = const Duration(seconds: 10),
    LocationAccuracy accuracy = LocationAccuracy.high,
  }) async {
    return await _service.getCurrentLocation(
      timeout: timeout,
      accuracy: accuracy,
    );
  }

  /// Check if location services are enabled
  Future<bool> isLocationEnabled() async {
    return await _service.isLocationServiceEnabled();
  }

  /// Check location permission status
  Future<LocationPermission> checkPermission() async {
    return await _service.getLocationPermission();
  }

  /// Request location permission
  Future<LocationPermission> requestPermission() async {
    return await _service.requestLocationPermission();
  }

  /// Get continuous location updates
  ///
  /// Example usage:
  /// ```dart
  /// final subscription = LocationUtil.instance.getLocationStream().listen(
  ///   (location) => print('New location: $location'),
  ///   onError: (error) => print('Location error: $error'),
  /// );
  /// // Don't forget to cancel: subscription.cancel();
  /// ```
  Stream<LocationData> getLocationStream({
    LocationAccuracy accuracy = LocationAccuracy.high,
    int distanceFilter = 10,
  }) {
    return _service.getLocationStream(
      accuracy: accuracy,
      distanceFilter: distanceFilter,
    );
  }

  /// Open location settings
  Future<void> openLocationSettings() async {
    await Geolocator.openLocationSettings();
  }

  /// Open app settings (for when permission is denied forever)
  Future<void> openAppSettings() async {
    await Geolocator.openAppSettings();
  }

  /// Calculate distance between two points in meters
  double calculateDistance({
    required double startLatitude,
    required double startLongitude,
    required double endLatitude,
    required double endLongitude,
  }) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  /// Calculate distance from current location to a point
  Future<double> calculateDistanceFromCurrent({
    required double targetLatitude,
    required double targetLongitude,
  }) async {
    final currentLocation = await getCurrentLocation();
    return calculateDistance(
      startLatitude: currentLocation.latitude,
      startLongitude: currentLocation.longitude,
      endLatitude: targetLatitude,
      endLongitude: targetLongitude,
    );
  }

  /// Get location with retry mechanism
  Future<LocationData> getCurrentLocationWithRetry({
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 2),
    Duration timeout = const Duration(seconds: 10),
    LocationAccuracy accuracy = LocationAccuracy.high,
  }) async {
    int attempts = 0;

    while (attempts < maxRetries) {
      try {
        return await getCurrentLocation(
          timeout: timeout,
          accuracy: accuracy,
        );
      } catch (e) {
        attempts++;
        if (attempts >= maxRetries) {
          rethrow;
        }
        await Future.delayed(retryDelay);
      }
    }

    throw const LocationException(
      'Failed to get location after all retries',
      LocationErrorType.unknown,
    );
  }
}

/// Extension for easy access from anywhere
extension LocationUtilExtension on LocationUtil {
  /// Quick access to get location with default settings
  Future<LocationData> getLocation() => getCurrentLocation();

  /// Quick check if location permission is granted
  Future<bool> hasPermission() async {
    final permission = await checkPermission();
    return permission == LocationPermission.always || permission == LocationPermission.whileInUse;
  }
}
