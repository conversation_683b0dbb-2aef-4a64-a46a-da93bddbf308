# LocationUtil - Fast, Clean & Testable Location Service

A comprehensive location utility for Flutter that provides easy access to device location with built-in error handling, retry mechanisms, and comprehensive testing support.

## ✨ Features

- **Fast**: Optimized for quick location retrieval with configurable timeouts
- **Clean**: Simple, intuitive API with clear method names
- **Easy Testing**: Built with dependency injection for easy mocking
- **Easy to Use**: Singleton pattern with extension methods for convenience
- **Comprehensive**: Handles permissions, errors, streams, and distance calculations
- **Robust**: Built-in retry mechanism and comprehensive error handling

## 🚀 Quick Start

### Basic Usage

```dart
import 'package:pl_coke_cooler/core/utils/location_util.dart';

// Get current location - simplest way
try {
  final location = await LocationUtil.instance.getLocation();
  print('Location: ${location.latitude}, ${location.longitude}');
} catch (e) {
  print('Error: $e');
}
```

### Advanced Usage

```dart
// Get location with custom settings
final location = await LocationUtil.instance.getCurrentLocation(
  timeout: Duration(seconds: 15),
  accuracy: LocationAccuracy.best,
);

// Get location with retry mechanism
final location = await LocationUtil.instance.getCurrentLocationWithRetry(
  maxRetries: 3,
  retryDelay: Duration(seconds: 2),
);

// Check permissions before getting location
if (await LocationUtil.instance.hasPermission()) {
  final location = await LocationUtil.instance.getLocation();
}
```

### Location Streaming

```dart
// Start continuous location updates
final subscription = LocationUtil.instance.getLocationStream(
  accuracy: LocationAccuracy.high,
  distanceFilter: 10, // Update every 10 meters
).listen(
  (location) => print('New location: $location'),
  onError: (error) => print('Error: $error'),
);

// Don't forget to cancel
subscription.cancel();
```

### Distance Calculations

```dart
// Calculate distance between two points
final distance = LocationUtil.instance.calculateDistance(
  startLatitude: 37.7749,
  startLongitude: -122.4194,
  endLatitude: 40.7589,
  endLongitude: -73.9851,
);

// Calculate distance from current location
final distance = await LocationUtil.instance.calculateDistanceFromCurrent(
  targetLatitude: 40.7589,
  targetLongitude: -73.9851,
);
```

## 🧪 Testing

The utility is designed for easy testing with dependency injection:

```dart
// In your test file
import 'package:flutter_test/flutter_test.dart';
import 'package:pl_coke_cooler/core/utils/location_util.dart';

void main() {
  test('should get location successfully', () async {
    // Arrange
    final mockLocation = LocationData(
      latitude: 37.7749,
      longitude: -122.4194,
      timestamp: DateTime.now(),
    );

    final mockService = MockLocationService(
      mockLocationData: mockLocation,
    );

    LocationUtil.initialize(locationService: mockService);

    // Act
    final result = await LocationUtil.instance.getCurrentLocation();

    // Assert
    expect(result, equals(mockLocation));
  });
}
```

## 🔧 Error Handling

The utility provides comprehensive error handling with specific exception types:

```dart
try {
  final location = await LocationUtil.instance.getCurrentLocation();
} on LocationException catch (e) {
  switch (e.type) {
    case LocationErrorType.permissionDenied:
      // Handle permission denied
      await LocationUtil.instance.requestPermission();
      break;
    case LocationErrorType.permissionDeniedForever:
      // Open app settings
      await LocationUtil.instance.openAppSettings();
      break;
    case LocationErrorType.serviceDisabled:
      // Open location settings
      await LocationUtil.instance.openLocationSettings();
      break;
    case LocationErrorType.timeout:
      // Handle timeout
      break;
    default:
      // Handle other errors
  }
}
```

## 📱 Usage in ViewModels/Controllers

```dart
class LocationViewModel {
  Future<void> updateLocation() async {
    try {
      final location = await LocationUtil.instance.getCurrentLocation();
      // Update state
    } on LocationException catch (e) {
      // Handle error
    }
  }
}
```

## 🛠️ Available Methods

| Method | Description |
|--------|-------------|
| `getCurrentLocation()` | Get current location with default settings |
| `getLocation()` | Quick access method (extension) |
| `getCurrentLocationWithRetry()` | Get location with automatic retry |
| `isLocationEnabled()` | Check if location services are enabled |
| `hasPermission()` | Quick permission check (extension) |
| `checkPermission()` | Get detailed permission status |
| `requestPermission()` | Request location permission |
| `getLocationStream()` | Get continuous location updates |
| `calculateDistance()` | Calculate distance between two points |
| `calculateDistanceFromCurrent()` | Calculate distance from current location |
| `openLocationSettings()` | Open device location settings |
| `openAppSettings()` | Open app settings |

## 🎯 Key Benefits

1. **Fast**: Single method call to get location
2. **Clean**: Clear, intuitive API design
3. **Easy Testing**: Mock-friendly architecture
4. **Easy to Use**: Works anywhere in your app
5. **Robust**: Comprehensive error handling
6. **Flexible**: Configurable timeouts, accuracy, and retry logic

## 📦 Dependencies

- `geolocator: ^13.0.1` - Added automatically to pubspec.yaml
- Built-in Flutter permissions handling
- No additional setup required

## 🎉 Ready to Use!

The LocationUtil is now ready to use throughout your application. Import it wherever you need location services and enjoy the clean, simple API! 