import 'package:common_widgets/constants/app_config_constant.dart';
import 'package:flutter/material.dart';

import 'theme/colors.dart';

GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void initCommonWidgets({
  required Color color,
  required GlobalKey<NavigatorState> parentNavigatorKey,
  required String baseUrl,
  required String token,
  required String mapTitleUrl,
  required String mapReferer,
}) {
  MyColors.primary = color;
  navigatorKey = parentNavigatorKey;
  AppConfig.init(baseUrl: baseUrl, token: token, mapTitleUrl: mapTitleUrl, mapReferer: mapReferer);
}
